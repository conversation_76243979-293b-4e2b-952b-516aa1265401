<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签分组功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.2s;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .tabs-info {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 标签分组功能测试</h1>
        
        <div class="tabs-info">
            <h3>📋 测试说明</h3>
            <p>此页面用于测试标签分组功能。请确保：</p>
            <ul>
                <li>已安装 Moment-Unified 扩展程序</li>
                <li>打开了多个不同域名的标签页（如：2个Google页面，1个GitHub页面）</li>
                <li>扩展程序有必要的权限（tabs, tabGroups）</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔍 1. 检查扩展程序状态</h3>
            <button class="test-button" onclick="checkExtensionStatus()">检查扩展状态</button>
            <div id="extensionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 2. 获取当前标签页信息</h3>
            <button class="test-button" onclick="getCurrentTabs()">获取标签页列表</button>
            <div id="tabsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📁 3. 测试分组功能</h3>
            <button class="test-button" onclick="testGrouping()">执行标签分组</button>
            <div id="groupingResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📂 4. 测试取消分组</h3>
            <button class="test-button" onclick="testUngrouping()">取消所有分组</button>
            <div id="ungroupingResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔄 5. 直接调用Chrome API</h3>
            <button class="test-button" onclick="directApiTest()">直接API测试</button>
            <div id="apiResult" class="result"></div>
        </div>
    </div>

    <script>
        // 检查扩展程序状态
        async function checkExtensionStatus() {
            const resultDiv = document.getElementById('extensionResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '检查中...';

            try {
                // 检查Chrome扩展API是否可用
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('Chrome扩展API不可用');
                }

                // 检查必要的权限
                const permissions = ['tabs', 'tabGroups'];
                const hasPermissions = await chrome.permissions.contains({
                    permissions: permissions
                });

                if (!hasPermissions) {
                    throw new Error('缺少必要权限: ' + permissions.join(', '));
                }

                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 扩展程序状态正常\n权限检查通过: ' + permissions.join(', ');
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 扩展程序检查失败:\n' + error.message;
            }
        }

        // 获取当前标签页
        async function getCurrentTabs() {
            const resultDiv = document.getElementById('tabsResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '获取中...';

            try {
                const tabs = await chrome.tabs.query({});
                const tabInfo = tabs.map(tab => ({
                    id: tab.id,
                    title: tab.title,
                    url: tab.url,
                    groupId: tab.groupId
                }));

                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 找到 ${tabs.length} 个标签页:\n` + 
                    JSON.stringify(tabInfo, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 获取标签页失败:\n' + error.message;
            }
        }

        // 测试分组功能
        async function testGrouping() {
            const resultDiv = document.getElementById('groupingResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '执行分组中...';

            try {
                // 发送消息到background script
                const response = await chrome.runtime.sendMessage({ action: 'groupTabs' });
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 分组操作完成:\n' + JSON.stringify(response, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 分组操作失败:\n' + error.message;
            }
        }

        // 测试取消分组
        async function testUngrouping() {
            const resultDiv = document.getElementById('ungroupingResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '取消分组中...';

            try {
                const response = await chrome.runtime.sendMessage({ action: 'ungroupTabs' });
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 取消分组完成:\n' + JSON.stringify(response, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 取消分组失败:\n' + error.message;
            }
        }

        // 直接API测试
        async function directApiTest() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '直接API测试中...';

            try {
                // 获取所有标签页
                const tabs = await chrome.tabs.query({});
                
                // 按域名分组
                const groups = {};
                tabs.forEach(tab => {
                    if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                        return;
                    }
                    
                    try {
                        const url = new URL(tab.url);
                        let domain = url.hostname.toLowerCase();
                        if (domain.startsWith('www.')) {
                            domain = domain.substring(4);
                        }
                        
                        if (!groups[domain]) {
                            groups[domain] = [];
                        }
                        groups[domain].push(tab);
                    } catch (e) {
                        console.warn('解析URL失败:', tab.url);
                    }
                });

                // 过滤只有一个标签的域名
                Object.keys(groups).forEach(domain => {
                    if (groups[domain].length < 2) {
                        delete groups[domain];
                    }
                });

                if (Object.keys(groups).length === 0) {
                    resultDiv.className = 'result info';
                    resultDiv.textContent = '⚠️ 没有找到可分组的标签页\n需要至少2个相同域名的标签页';
                    return;
                }

                // 创建分组
                const colors = ['blue', 'red', 'yellow', 'green', 'pink', 'purple'];
                let colorIndex = 0;
                let successCount = 0;

                for (const [domain, domainTabs] of Object.entries(groups)) {
                    try {
                        const tabIds = domainTabs.map(tab => tab.id);
                        const groupId = await chrome.tabs.group({ tabIds });
                        
                        await chrome.tabGroups.update(groupId, {
                            title: domain,
                            color: colors[colorIndex % colors.length]
                        });
                        
                        colorIndex++;
                        successCount++;
                    } catch (error) {
                        console.error(`创建分组失败 ${domain}:`, error);
                    }
                }

                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 直接API测试成功!\n创建了 ${successCount} 个分组:\n` + 
                    Object.keys(groups).map(domain => `${domain}: ${groups[domain].length}个标签页`).join('\n');
                    
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 直接API测试失败:\n' + error.message;
            }
        }

        // 页面加载时自动检查状态
        window.addEventListener('load', () => {
            checkExtensionStatus();
        });
    </script>
</body>
</html>
