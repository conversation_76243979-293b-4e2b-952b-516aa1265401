{"name": "<PERSON><PERSON><PERSON>", "version": "5.0.10", "publishConfig": {"tag": "v5-legacy"}, "type": "module", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "bin": "./dist/esm/bin.mjs", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist"], "description": "A deep deletion module for node (like `rm -rf`)", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "repository": "git://github.com/isaacs/rimraf.git", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --log-level warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/node": "^20.12.11", "mkdirp": "^3.0.1", "prettier": "^3.2.5", "tap": "^19.0.1", "tshy": "^1.14.0", "typedoc": "^0.25.13", "typescript": "^5.4.5"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "dependencies": {"glob": "^10.3.7"}, "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "module": "./dist/esm/index.js"}