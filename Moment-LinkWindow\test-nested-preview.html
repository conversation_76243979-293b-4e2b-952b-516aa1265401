<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌套弹窗功能测试 - Moment-LinkWindow</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-top: 0;
            font-size: 1.5em;
        }

        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .test-link {
            display: block;
            padding: 15px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .test-link:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        .instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .instructions h3 {
            color: #155724;
            margin-top: 0;
        }

        .instructions p {
            color: #155724;
            margin-bottom: 0;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
        }

        .status-pending { background: #ffc107; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }

        .nested-content {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .nested-content h4 {
            color: #856404;
            margin-top: 0;
        }

        .nested-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nested-links a {
            padding: 8px 15px;
            background: #fd7e14;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .nested-links a:hover {
            background: #e8590c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 嵌套弹窗功能测试</h1>

        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p><strong>操作方式：</strong>按住 <kbd>Alt</kbd> 键 + 点击链接来触发弹窗预览</p>
            <p><strong>嵌套测试：</strong>在弹出的预览窗口内，继续按住 <kbd>Alt</kbd> 键 + 点击其他链接，应该能够触发新的嵌套弹窗</p>
        </div>

        <div class="test-section">
            <h2>🌐 基础链接测试</h2>
            <p>这些链接用于测试基本的弹窗功能：</p>
            <div class="test-links">
                <a href="https://www.baidu.com" class="test-link">百度首页</a>
                <a href="https://www.github.com" class="test-link">GitHub</a>
                <a href="https://www.google.com" class="test-link">Google</a>
                <a href="https://www.stackoverflow.com" class="test-link">Stack Overflow</a>
            </div>
        </div>

        <div class="test-section">
            <h2>🔄 嵌套链接测试页面</h2>
            <p>这些页面包含更多链接，用于测试嵌套弹窗功能：</p>
            <div class="test-links">
                <a href="https://news.ycombinator.com" class="test-link">Hacker News (多链接页面)</a>
                <a href="https://www.reddit.com" class="test-link">Reddit (多链接页面)</a>
                <a href="https://www.wikipedia.org" class="test-link">Wikipedia (多链接页面)</a>
            </div>
        </div>

        <div class="test-section">
            <h2>📄 本地测试内容</h2>
            <div class="nested-content">
                <h4>🎯 嵌套测试区域</h4>
                <p>在弹窗中，这些链接应该能够触发新的嵌套弹窗：</p>
                <div class="nested-links">
                    <a href="https://developer.mozilla.org">MDN Web Docs</a>
                    <a href="https://www.w3schools.com">W3Schools</a>
                    <a href="https://css-tricks.com">CSS-Tricks</a>
                    <a href="https://codepen.io">CodePen</a>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 功能验证清单</h2>
            <ul class="feature-list">
                <li><span class="status-indicator status-pending"></span>基本链接预览功能（Alt+点击）</li>
                <li><span class="status-indicator status-pending"></span>弹窗内链接的嵌套触发</li>
                <li><span class="status-indicator status-pending"></span>嵌套弹窗的层级管理（z-index）</li>
                <li><span class="status-indicator status-pending"></span>窗口点击置顶功能</li>
                <li><span class="status-indicator status-pending"></span>多层嵌套弹窗的正确显示</li>
                <li><span class="status-indicator status-pending"></span>嵌套弹窗的正确关闭和清理</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🐛 已知问题修复验证</h2>
            <p><strong>修复前问题：</strong>在弹窗内点击链接无法触发新的弹窗</p>
            <p><strong>修复后期望：</strong>弹窗内的链接应该能够正常触发新的独立弹窗，支持无限嵌套</p>
            
            <div class="nested-content">
                <h4>🧪 具体测试步骤</h4>
                <ol>
                    <li>按住 Alt 键点击上方任意链接，应该弹出预览窗口</li>
                    <li>在弹出的预览窗口内，找到页面中的其他链接</li>
                    <li>继续按住 Alt 键点击预览窗口内的链接</li>
                    <li>应该弹出新的预览窗口，且层级正确（新窗口在上层）</li>
                    <li>重复步骤3-4，测试多层嵌套</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔗 嵌套弹窗测试页面已加载');
            console.log('📋 请按住 Alt 键并点击链接来测试弹窗功能');
            
            // 检查扩展是否已加载
            setTimeout(() => {
                if (typeof LinkWindowPreviewManager !== 'undefined') {
                    console.log('✅ Moment-LinkWindow 扩展已检测到');
                } else {
                    console.warn('⚠️ 未检测到 Moment-LinkWindow 扩展，请确保扩展已安装并启用');
                }
            }, 1000);
        });

        // 添加测试结果记录功能
        let testResults = {
            basicPreview: false,
            nestedPreview: false,
            zIndexManagement: false,
            clickToFront: false,
            multiLevel: false,
            cleanup: false
        };

        // 监听弹窗创建事件（如果扩展提供了相关事件）
        document.addEventListener('linkPreviewCreated', function(e) {
            console.log('🎉 检测到弹窗创建事件:', e.detail);
            updateTestStatus('basicPreview', true);
        });

        function updateTestStatus(testName, success) {
            testResults[testName] = success;
            console.log('📊 测试状态更新:', testName, success ? '✅' : '❌');
            
            // 更新UI状态指示器
            const indicators = document.querySelectorAll('.status-indicator');
            indicators.forEach((indicator, index) => {
                const testNames = Object.keys(testResults);
                if (testNames[index] && testResults[testNames[index]]) {
                    indicator.className = 'status-indicator status-success';
                }
            });
        }
    </script>
</body>
</html>
