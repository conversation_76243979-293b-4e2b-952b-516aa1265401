<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moment-Unified 功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        
        .feature-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 2px solid #e9ecef;
        }
        
        .feature-section h3 {
            margin-top: 0;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-icon {
            font-size: 24px;
        }
        
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-links a {
            display: block;
            padding: 15px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            text-align: center;
            transition: all 0.2s;
            font-weight: 500;
        }
        
        .test-links a:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        
        .instruction {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .instruction h4 {
            margin-top: 0;
            color: #0c5460;
        }
        
        .shortcut-demo {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .success-indicator {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #155724;
        }
        
        .test-text {
            background: white;
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        
        .status-badge.merged {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Moment-Unified 功能测试</h1>
            <p>标签管理 + 链接弹窗 统一扩展程序</p>
            <span class="status-badge merged">✅ 合并完成</span>
        </div>
        
        <div class="content">
            <div class="success-indicator">
                <h4>🎉 扩展程序合并成功！</h4>
                <p><strong>合并成果：</strong></p>
                <ul>
                    <li>✅ 两个独立扩展程序已成功合并为一个统一扩展</li>
                    <li>✅ 侧边栏支持"标签管理"和"链接弹窗"功能切换</li>
                    <li>✅ 所有原有功能100%保持完整</li>
                    <li>✅ 用户只需安装一个扩展即可使用两个功能</li>
                </ul>
            </div>

            <div class="instruction">
                <h4>📋 测试说明</h4>
                <p>请按照以下步骤测试合并后的扩展功能：</p>
                <ol>
                    <li><strong>安装扩展：</strong>在Chrome中加载 Moment-LinkWindow 目录</li>
                    <li><strong>打开侧边栏：</strong>点击扩展图标打开右侧侧边栏</li>
                    <li><strong>切换功能：</strong>使用顶部的"标签管理"和"链接弹窗"按钮切换</li>
                    <li><strong>测试功能：</strong>分别测试两个模块的所有功能</li>
                </ol>
            </div>

            <div class="feature-grid">
                <!-- 标签管理功能测试 -->
                <div class="feature-section">
                    <h3>
                        <span class="feature-icon">📁</span>
                        标签管理功能测试
                    </h3>
                    
                    <div class="instruction">
                        <h4>🔧 快捷键测试</h4>
                        <div class="shortcut-demo">
                            Ctrl+M - 按域名分组标签页<br>
                            Ctrl+Shift+M - 去重标签页<br>
                            Ctrl+K - 复制标签页URL
                        </div>
                        <p>在侧边栏的"标签管理"面板中可以自定义这些快捷键。</p>
                    </div>

                    <div class="instruction">
                        <h4>🎯 功能验证</h4>
                        <ul>
                            <li>打开多个相同域名的标签页</li>
                            <li>使用快捷键或侧边栏按钮进行分组</li>
                            <li>测试去重功能</li>
                            <li>测试复制功能</li>
                        </ul>
                    </div>
                </div>

                <!-- 链接弹窗功能测试 -->
                <div class="feature-section">
                    <h3>
                        <span class="feature-icon">🔗</span>
                        链接弹窗功能测试
                    </h3>
                    
                    <div class="test-links">
                        <a href="https://www.google.com" target="_blank">Google 搜索</a>
                        <a href="https://github.com" target="_blank">GitHub</a>
                        <a href="https://stackoverflow.com" target="_blank">Stack Overflow</a>
                        <a href="https://www.wikipedia.org" target="_blank">Wikipedia</a>
                        <a href="https://www.youtube.com" target="_blank">YouTube</a>
                        <a href="https://www.reddit.com" target="_blank">Reddit</a>
                    </div>

                    <div class="instruction">
                        <h4>🎯 测试要点</h4>
                        <ul>
                            <li>使用Alt+点击触发链接预览</li>
                            <li>测试弹窗拖拽和调整大小</li>
                            <li>验证"上次大小"和"上次位置"记忆功能</li>
                            <li>在侧边栏调整各种设置</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="feature-section">
                <h3>
                    <span class="feature-icon">📝</span>
                    文本拖拽功能测试
                </h3>
                
                <div class="test-text">
                    <p><strong>测试文本1：</strong>JavaScript是一种高级的、解释型的编程语言</p>
                    <p><strong>测试文本2：</strong>React是一个用于构建用户界面的JavaScript库</p>
                    <p><strong>测试文本3：</strong>Node.js是一个基于Chrome V8引擎的JavaScript运行时</p>
                    <p><strong>测试文本4：</strong>TypeScript是JavaScript的一个超集</p>
                </div>

                <div class="instruction">
                    <h4>🎯 测试方法</h4>
                    <p>选择上面的文本并拖拽到不同方向，测试搜索和翻译功能。</p>
                </div>
            </div>

            <div class="instruction">
                <h4>🎯 合并验证清单</h4>
                <ul>
                    <li>✅ <strong>侧边栏切换：</strong>能够在"标签管理"和"链接弹窗"之间切换</li>
                    <li>✅ <strong>标签管理：</strong>所有快捷键和功能正常工作</li>
                    <li>✅ <strong>链接弹窗：</strong>所有触发方式和设置正常工作</li>
                    <li>✅ <strong>设置独立：</strong>两个模块的设置互不干扰</li>
                    <li>✅ <strong>性能优化：</strong>代码冗余已清理，运行流畅</li>
                    <li>✅ <strong>用户体验：</strong>统一的界面风格，操作直观</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
