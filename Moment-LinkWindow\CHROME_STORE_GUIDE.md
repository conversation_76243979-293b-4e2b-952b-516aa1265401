# Chrome 应用商店发布指南

本文档提供了将 Moment-LinkWindow 扩展程序发布到 Chrome Web Store 的详细指南。

## 📋 发布前检查清单

### 代码质量
- [ ] 所有功能正常工作
- [ ] 没有控制台错误
- [ ] 代码已经过测试
- [ ] 性能优化完成
- [ ] 安全审查通过

### 文档完整性
- [ ] README.md 完整且准确
- [ ] PRIVACY.md 隐私政策完整
- [ ] LICENSE 许可证文件存在
- [ ] 功能说明清晰

### 资源文件
- [ ] 所有图标文件完整（16, 32, 48, 64, 128, 256, 512px）
- [ ] 图标设计符合 Google 设计规范
- [ ] 截图和宣传图片准备完成

## 🎨 应用商店资源

### 图标要求
- **尺寸**：128x128 像素（主要图标）
- **格式**：PNG 格式
- **设计**：简洁、清晰、符合 Material Design 规范
- **背景**：透明或白色背景

### 截图要求
- **尺寸**：1280x800 或 640x400 像素
- **数量**：至少 1 张，最多 5 张
- **内容**：展示主要功能和用户界面
- **质量**：高清晰度，无水印

### 宣传图片（可选）
- **小图标**：440x280 像素
- **大图标**：920x680 像素
- **用途**：在应用商店中展示

## 📝 应用商店信息

### 基本信息
```
名称：Moment-LinkWindow
简短描述：智能链接预览与文本拖拽扩展程序
详细描述：见下方详细描述模板
类别：生产力工具
语言：中文（简体）、英语
```

### 详细描述模板
```
🔗 Moment-LinkWindow - 智能链接预览与文本拖拽扩展程序

让您的浏览体验更加高效便捷！

✨ 主要功能：

🔗 智能链接预览
• 按住快捷键点击链接，在弹窗中预览网页内容
• 支持多种触发方式：快捷键+点击、悬停、长按等
• 可拖拽移动、调整大小、最小化窗口
• 支持多窗口同时预览
• 个性化设置：自定义颜色、大小、位置

🖱️ 文本拖拽功能
• 选中文本后拖拽即可快速搜索或翻译
• 支持四个方向的拖拽操作
• 多引擎支持：百度、Google、Bing
• 支持中英文翻译
• 结果在预览窗口中显示，无需跳转

⚙️ 丰富的自定义选项
• 自定义触发快捷键（Alt、Ctrl、Shift、Meta）
• 调节触发延迟时间
• 选择窗口大小和位置
• 设置窗口颜色和透明度
• 配置各方向拖拽动作

🔒 隐私安全
• 所有设置仅存储在本地
• 不收集任何个人信息
• 开源透明，安全可靠

🚀 使用方法：
1. 安装扩展程序
2. 点击扩展程序图标进行设置
3. 按住 Alt 键点击链接预览网页
4. 选中文本拖拽进行搜索或翻译

立即体验更智能的浏览方式！
```

### 关键词
```
链接预览, 文本拖拽, 搜索, 翻译, 生产力, 浏览器工具, 快捷操作, 网页预览
```

## 🔐 权限说明

在应用商店中需要详细说明每个权限的用途：

### storage
**用途**：存储用户的个性化设置
**说明**：保存用户的偏好设置，如触发方式、窗口大小、颜色等配置

### activeTab
**用途**：在当前标签页中提供链接预览功能
**说明**：仅在用户主动触发时访问当前标签页内容

### scripting
**用途**：注入必要的脚本以实现功能
**说明**：在网页中注入链接预览和文本拖拽功能脚本

### sidePanel
**用途**：显示设置侧边栏
**说明**：提供用户友好的设置界面

### host_permissions: <all_urls>
**用途**：在所有网站上提供链接预览功能
**说明**：确保扩展程序在任何网站上都能正常工作

## 📊 发布流程

### 1. 准备阶段
1. 完成所有功能开发和测试
2. 准备所有必需的资源文件
3. 编写完整的应用商店描述
4. 创建开发者账户（需要 $5 注册费）

### 2. 上传阶段
1. 登录 [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
2. 点击"Add new item"
3. 上传扩展程序的 ZIP 包
4. 填写所有必需信息
5. 上传图标和截图

### 3. 审核阶段
- Google 会审核扩展程序（通常需要几天到几周）
- 确保符合 [Chrome Web Store 政策](https://developer.chrome.com/docs/webstore/program-policies/)
- 可能需要回应审核反馈

### 4. 发布阶段
- 审核通过后选择发布时间
- 可以选择立即发布或定时发布
- 发布后用户即可搜索和安装

## 🔍 审核要点

### 功能性
- 扩展程序必须按描述正常工作
- 所有功能都应该可用
- 不能有明显的 bug 或错误

### 用户体验
- 界面友好，易于使用
- 设置选项清晰明了
- 性能良好，不影响浏览器速度

### 安全性
- 不包含恶意代码
- 权限使用合理且必要
- 隐私政策完整准确

### 政策合规
- 符合 Chrome Web Store 政策
- 不违反版权或商标
- 内容适当，无有害信息

## 📈 发布后维护

### 用户反馈
- 及时回应用户评论和问题
- 收集用户建议进行改进
- 维护良好的评分和口碑

### 版本更新
- 定期发布更新修复 bug
- 添加新功能响应用户需求
- 保持与浏览器新版本的兼容性

### 数据分析
- 监控安装量和使用情况
- 分析用户行为优化功能
- 根据数据调整发展策略

## 📞 支持资源

- [Chrome 扩展程序开发文档](https://developer.chrome.com/docs/extensions/)
- [Chrome Web Store 政策](https://developer.chrome.com/docs/webstore/program-policies/)
- [开发者支持](https://support.google.com/chrome_webstore/contact/developer_support)

---

祝您发布成功！🎉
