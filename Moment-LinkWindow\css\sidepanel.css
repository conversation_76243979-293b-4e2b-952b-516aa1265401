/* Moment-Unified 侧边栏样式 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background: #f8f9fa;
    overflow-x: hidden;
}

.sidepanel-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.sidepanel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.header-text h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.header-text p {
    margin: 4px 0 0 0;
    font-size: 12px;
    opacity: 0.9;
}

/* 标签导航样式 */
.tab-navigation {
    display: flex;
    background: white;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: #f8f9fa;
    color: #495057;
}

.tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: #f8f9ff;
}

.tab-icon {
    font-size: 16px;
}

/* 标签内容区域 */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 主要内容区域 */
.sidepanel-main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* 设置分组 */
.settings-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e5e9;
}

.settings-section h2 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #667eea;
    padding-bottom: 8px;
}

/* 标签管理专用样式 - 完全还原原始TabManager样式 */

/* 快速操作按钮 */
.quick-actions {
    margin-bottom: 24px;
}

.btn-action {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    margin-bottom: 8px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    background: #ffffff;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    text-align: left;
    position: relative;
}

.btn-action:hover {
    background: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

/* 移除特殊按钮样式，统一所有按钮外观 */

.btn-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.btn-text {
    flex: 1;
    font-weight: 500;
}

.btn-shortcut {
    font-size: 11px;
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.btn-shortcut {
    background: rgba(0, 0, 0, 0.1);
}

/* 状态信息 */
.status-section {
    margin-bottom: 24px;
}

.status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.status-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    text-align: center;
}

.status-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
}

.status-value {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #007bff;
}

/* 设置区域 */
.settings-section {
    margin-bottom: 24px;
}

.setting-item {
    margin-bottom: 16px;
}

.setting-label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

/* 快捷键输入样式 */
.shortcut-input-wrapper {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    background: #ffffff;
    overflow: hidden;
}

.shortcut-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
}

.shortcut-keys {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    font-weight: 500;
    color: #007bff;
    background: #e7f3ff;
    padding: 4px 8px;
    border-radius: 4px;
}

.shortcut-edit-btn {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.shortcut-edit-btn:hover {
    background: #f8f9fa;
    color: #007bff;
}

.shortcut-input-container {
    border-top: 1px solid #e1e5e9;
    padding: 12px;
    background: #f8f9fa;
}

.shortcut-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    background: #ffffff;
    margin-bottom: 8px;
}

.shortcut-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.shortcut-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.shortcut-save-btn,
.shortcut-cancel-btn {
    padding: 6px 12px;
    border: 1px solid;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.shortcut-save-btn {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.shortcut-save-btn:hover:not(:disabled) {
    background: #0056b3;
    border-color: #0056b3;
}

.shortcut-save-btn:disabled {
    background: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.shortcut-cancel-btn {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.shortcut-cancel-btn:hover {
    background: #5a6268;
    border-color: #545b62;
}

/* 复制模态框样式 */
.copy-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.2s ease;
}

.copy-modal {
    background: white;
    border-radius: 8px;
    padding: 20px;
    min-width: 250px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.2s ease;
}

.copy-modal h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #333;
    text-align: center;
}

.copy-option-btn {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    margin-bottom: 8px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    background: #ffffff;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    text-align: left;
}

.copy-option-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-1px);
}

.copy-cancel-btn {
    width: 100%;
    padding: 10px;
    border: 1px solid #6c757d;
    border-radius: 6px;
    background: #6c757d;
    color: white;
    cursor: pointer;
    font-size: 14px;
    margin-top: 8px;
    transition: all 0.2s ease;
}

.copy-cancel-btn:hover {
    background: #5a6268;
    border-color: #545b62;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}



/* 设置项 */
.setting-item {
    margin-bottom: 16px;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #555;
    font-size: 13px;
}

/* 输入控件样式 */
.setting-select,
.setting-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 13px;
    background: white;
    transition: all 0.2s ease;
}

.setting-select:focus,
.setting-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-select:hover,
.setting-input:hover {
    border-color: #bbb;
}

/* 滑块容器 */
.slider-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.setting-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #e1e5e9;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.setting-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.setting-slider::-webkit-slider-thumb:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.setting-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-value {
    min-width: 50px;
    text-align: right;
    font-weight: 500;
    color: #667eea;
    font-size: 12px;
}

/* 快捷键输入 */
.shortcut-input-container {
    display: flex;
    gap: 8px;
}

.shortcut-btn {
    padding: 8px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.shortcut-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

/* 颜色选择器 */
.color-picker-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.color-picker {
    width: 40px;
    height: 32px;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    background: none;
    padding: 0;
}

.color-presets {
    display: flex;
    gap: 6px;
}

.color-preset {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.color-preset:hover {
    transform: scale(1.1);
    border-color: #667eea;
}

/* 延迟描述 */
.delay-description {
    font-size: 11px;
    color: #666;
    margin-top: 4px;
    font-style: italic;
}

.delay-description.disabled {
    color: #999;
}

/* 操作按钮 */
.settings-actions {
    display: flex;
    gap: 12px;
    padding: 20px;
    background: white;
    border-top: 1px solid #e1e5e9;
    margin-top: auto;
}

.btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* 状态消息 */
.status-message {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transition: all 0.3s ease;
}

.status-message.success {
    background: #28a745;
    color: white;
}

.status-message.error {
    background: #dc3545;
    color: white;
}

.status-message.info {
    background: #17a2b8;
    color: white;
}

.status-message.hidden {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
    pointer-events: none;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    transition: all 0.3s ease;
}

.modal.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal-content {
    background: white;
    border-radius: 8px;
    padding: 24px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transform: scale(1);
    transition: all 0.3s ease;
}

.modal.hidden .modal-content {
    transform: scale(0.9);
}

.modal-content h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    color: #2c3e50;
}

.modal-content p {
    margin: 0 0 16px 0;
    color: #666;
    font-size: 14px;
}

.shortcut-display {
    background: #f8f9fa;
    border: 2px dashed #ddd;
    border-radius: 6px;
    padding: 16px;
    text-align: center;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 20px;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.modal-actions .btn {
    flex: none;
    min-width: 80px;
}

/* 禁用状态 */
.disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* 滚动条样式 */
.sidepanel-main::-webkit-scrollbar {
    width: 6px;
}

.sidepanel-main::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidepanel-main::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidepanel-main::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式适配 */
@media (max-width: 320px) {
    .sidepanel-header {
        padding: 16px;
    }
    
    .sidepanel-main {
        padding: 16px;
    }
    
    .settings-section {
        padding: 16px;
    }
    
    .settings-actions {
        flex-direction: column;
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .tab-button {
        padding: 12px 16px;
        font-size: 13px;
    }
}

/* 复选框样式 */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 500;
    color: #555;
    font-size: 13px;
}

.setting-checkbox {
    display: none;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    background: white;
    position: relative;
    transition: all 0.2s ease;
}

.setting-checkbox:checked + .checkbox-custom {
    background: #007bff;
    border-color: #007bff;
}

.setting-checkbox:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.setting-description {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    line-height: 1.4;
}

/* 状态消息样式 */
.status-message {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-message.hidden {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
    pointer-events: none;
}
