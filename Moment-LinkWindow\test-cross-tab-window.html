<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨标签页窗口链接弹窗测试 - Moment-LinkWindow</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-top: 0;
            font-size: 1.5em;
        }

        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .test-link {
            display: block;
            padding: 15px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .test-link:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        .instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .instructions h3 {
            color: #155724;
            margin-top: 0;
        }

        .instructions p {
            color: #155724;
            margin-bottom: 0;
        }

        .problem-info {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .problem-info h3 {
            color: #721c24;
            margin-top: 0;
        }

        .problem-info p {
            color: #721c24;
            margin-bottom: 0;
        }

        .solution-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .solution-info h3 {
            color: #0c5460;
            margin-top: 0;
        }

        .solution-info ul {
            color: #0c5460;
            margin-bottom: 0;
        }

        .test-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .test-steps h3 {
            color: #856404;
            margin-top: 0;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }

        .step-number {
            background: #007bff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .step-description {
            flex: 1;
        }

        .cross-tab-indicator {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .cross-tab-indicator h3 {
            color: #007bff;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪟 跨标签页窗口链接弹窗测试</h1>

        <div class="cross-tab-indicator">
            <h3>🔍 环境检测</h3>
            <p id="environment-status">正在检测当前环境...</p>
        </div>

        <div class="instructions">
            <h3>📋 测试目标</h3>
            <p><strong>验证跨标签页窗口中的链接弹窗功能：</strong>确保在跨标签页窗口中触发的新链接弹窗是完全独立的弹窗，而不是被限制在父窗口内部</p>
        </div>

        <div class="problem-info">
            <h3>🐛 修复的问题</h3>
            <p><strong>修复前错误行为：</strong>在跨标签页窗口中触发的新链接弹窗被限制在跨标签页窗口内部，表现为子窗口或内嵌弹窗</p>
            <p><strong>修复后正确行为：</strong>在跨标签页窗口中触发的新链接弹窗应该是完全独立的弹窗，具有正确的层级管理</p>
        </div>

        <div class="solution-info">
            <h3>✅ 修复方案</h3>
            <ul>
                <li>在跨标签页窗口创建时添加特殊标识参数</li>
                <li>自动注入内容脚本以支持链接弹窗功能</li>
                <li>检测跨标签页窗口环境并调整弹窗创建逻辑</li>
                <li>确保跨标签页窗口中的弹窗有正确的z-index层级</li>
                <li>所有触发方式在跨标签页窗口中都能正常工作</li>
            </ul>
        </div>

        <div class="test-steps">
            <h3>🧪 测试步骤</h3>
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-description">在普通页面中，按住Alt键点击下方任意链接创建弹窗</div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-description">点击弹窗右上角的"📌"按钮，升级为跨标签页窗口</div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-description">在新打开的跨标签页窗口中，继续按住Alt键点击页面中的其他链接</div>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-description">验证新弹窗是独立的，不被限制在跨标签页窗口内部</div>
            </div>
            <div class="step">
                <div class="step-number">5</div>
                <div class="step-description">测试所有触发方式（点击、悬停、长按、拖拽等）</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 第一步：创建初始弹窗</h2>
            <p>使用这些链接创建初始弹窗，然后升级为跨标签页窗口：</p>
            <div class="test-links">
                <a href="https://www.baidu.com" class="test-link">百度首页</a>
                <a href="https://www.github.com" class="test-link">GitHub</a>
                <a href="https://www.google.com" class="test-link">Google</a>
                <a href="https://www.stackoverflow.com" class="test-link">Stack Overflow</a>
            </div>
        </div>

        <div class="test-section">
            <h2>🪟 第二步：跨标签页窗口中的链接测试</h2>
            <p>在跨标签页窗口中，这些链接应该能够触发独立的弹窗：</p>
            <div class="test-links">
                <a href="https://news.ycombinator.com" class="test-link">Hacker News</a>
                <a href="https://www.reddit.com" class="test-link">Reddit</a>
                <a href="https://www.wikipedia.org" class="test-link">Wikipedia</a>
                <a href="https://developer.mozilla.org" class="test-link">MDN</a>
                <a href="https://www.w3schools.com" class="test-link">W3Schools</a>
                <a href="https://css-tricks.com" class="test-link">CSS-Tricks</a>
            </div>
        </div>

        <div class="test-section">
            <h2>✅ 验证清单</h2>
            <div class="solution-info">
                <h3>🔍 检查要点</h3>
                <ul>
                    <li><strong>环境检测：</strong>页面能够正确检测是否在跨标签页窗口中</li>
                    <li><strong>独立弹窗：</strong>跨标签页窗口中的链接触发独立弹窗，不是内嵌弹窗</li>
                    <li><strong>层级正确：</strong>新弹窗有正确的z-index，显示在页面内容之上</li>
                    <li><strong>所有触发方式：</strong>点击、悬停、长按、拖拽等都能正常工作</li>
                    <li><strong>功能完整：</strong>弹窗的所有功能（关闭、最小化、拖拽等）都正常</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🪟 跨标签页窗口链接弹窗测试页面已加载');
            
            // 检测环境
            const statusElement = document.getElementById('environment-status');
            const urlParams = new URLSearchParams(window.location.search);
            const isCrossTabWindow = urlParams.has('__moment_linkwindow_cross_tab');
            
            if (isCrossTabWindow) {
                statusElement.innerHTML = '✅ <strong>当前在跨标签页窗口中</strong> - 可以测试链接弹窗功能';
                statusElement.style.color = '#155724';
                console.log('✅ 检测到跨标签页窗口环境');
            } else {
                statusElement.innerHTML = '📄 <strong>当前在普通页面中</strong> - 请先创建弹窗并升级为跨标签页窗口';
                statusElement.style.color = '#856404';
                console.log('📄 当前在普通页面环境');
            }
            
            setTimeout(() => {
                if (typeof LinkWindowPreviewManager !== 'undefined') {
                    console.log('✅ Moment-LinkWindow 扩展已检测到');
                    console.log('🎯 开始测试：跨标签页窗口中的链接弹窗功能');
                } else {
                    console.warn('⚠️ 未检测到 Moment-LinkWindow 扩展');
                }
            }, 1000);
        });
    </script>
</body>
</html>
