# Moment-LinkWindow

🔗 智能链接预览与文本拖拽扩展程序

## 📖 简介

Moment-LinkWindow 是一个功能强大的 Chrome 浏览器扩展程序，提供智能链接预览和文本拖拽功能。通过简单的快捷键操作，您可以在不离开当前页面的情况下预览链接内容，或通过拖拽文本快速进行搜索和翻译。

## ✨ 主要功能

### 🔗 链接预览功能
- **智能预览**：按住快捷键点击链接，在弹窗中预览网页内容
- **多种触发方式**：支持快捷键+点击、快捷键+悬停、长按、拖拽、悬停等多种触发方式
- **自定义快捷键**：支持 Alt、Ctrl、Shift、Meta 等修饰键
- **灵活窗口管理**：可拖拽移动、调整大小、最小化、跨标签页显示
- **多窗口支持**：同时打开多个预览窗口，支持嵌套预览
- **个性化设置**：自定义窗口大小、位置、颜色、透明度等

### 🖱️ 文本拖拽功能
- **方向识别**：支持向上、下、左、右四个方向的拖拽操作
- **智能动作**：根据拖拽方向执行搜索或翻译操作
- **多引擎支持**：支持百度、Google、Bing 等搜索和翻译引擎
- **语言选择**：支持中文、英文等目标语言翻译
- **即时预览**：拖拽结果在预览窗口中显示，无需跳转页面

## 🚀 安装方法

### 开发者模式安装（当前版本）
1. 下载或克隆本项目到本地
2. 打开 Chrome 浏览器，访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载未打包的扩展程序"
5. 选择项目的 `Moment-LinkWindow` 文件夹
6. 扩展程序安装成功后，点击浏览器右上角的扩展程序图标进行设置

### Chrome 应用商店安装（即将上线）
- 即将在 Chrome Web Store 上架，敬请期待

## 🎯 使用方法

### 链接预览
1. **默认方式**：按住 `Alt` 键 + 点击任意链接
2. **自定义设置**：点击扩展程序图标，在侧边栏中配置：
   - 触发方式（快捷键+点击、悬停等）
   - 自定义快捷键（Alt、Ctrl、Shift、Meta）
   - 触发延迟时间
   - 窗口大小（小、中、大、上次大小）
   - 窗口位置（左侧、中间、右侧、跟随鼠标、上次位置）
   - 窗口颜色和透明度
   - 窗口背景效果

### 文本拖拽
1. **选择文本**：在网页中选中任意文本内容
2. **拖拽操作**：按住鼠标左键拖拽选中的文本
3. **方向动作**：
   - **向上拖拽**：执行搜索操作（默认）
   - **向下拖拽**：执行翻译操作（默认）
   - **向左拖拽**：执行搜索操作（默认）
   - **向右拖拽**：执行搜索操作（默认）
4. **自定义设置**：在侧边栏中配置：
   - 各方向的动作（搜索、翻译、无）
   - 搜索引擎选择（百度、Google、Bing）
   - 翻译引擎选择（百度翻译、Google翻译、Bing翻译）
   - 目标语言（中文、英文）

## ⚙️ 设置选项

### 链接弹窗基础设置
- **触发方式**：自定义快捷键+点击、自定义快捷键+悬停、长按、拖拽、悬停
- **自定义快捷键**：Alt、Ctrl、Shift、Meta
- **触发延迟**：0-2000ms 可调节
- **弹窗大小**：小(500×600)、中(700×800)、大(900×800)、上次大小
- **弹窗位置**：左侧、中间、右侧、跟随鼠标、上次位置
- **弹窗颜色**：自定义颜色选择器 + 预设颜色
- **背景透明度**：10%-100% 可调节
- **弹窗背景**：默认、透明、模糊

### 文字拖拽功能设置
- **文本拖拽总开关**：启用/禁用
- **向上拖拽**：搜索、翻译、无
- **向下拖拽**：搜索、翻译、无
- **向左拖拽**：搜索、翻译、无
- **向右拖拽**：搜索、翻译、无
- **搜索引擎**：百度、谷歌、Bing
- **翻译引擎**：百度翻译、谷歌翻译、Bing翻译
- **目标语言**：中文、英文

## 🔧 技术特性

- **Manifest V3**：使用最新的 Chrome 扩展程序标准
- **侧边栏界面**：现代化的设置界面，易于使用
- **实时同步**：设置在所有标签页间实时同步
- **性能优化**：轻量级设计，不影响浏览器性能
- **兼容性强**：支持所有现代网站和页面
- **安全可靠**：严格的权限控制，保护用户隐私

## 📋 系统要求

- Chrome 浏览器版本 88 或更高
- 支持 Manifest V3 的浏览器环境
- 需要 `storage`、`activeTab`、`scripting`、`sidePanel` 权限

## 🐛 问题反馈

如果您在使用过程中遇到任何问题，请：

1. 检查扩展程序是否正确安装并启用
2. 确认触发方式是否正确（默认：Alt + 点击链接）
3. 查看浏览器控制台是否有错误信息
4. 确认扩展程序权限是否正确授予

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来帮助改进这个项目。

## 📞 联系我们

- 项目主页：[GitHub Repository](https://github.com/moment-team/link-window)
- 问题反馈：[GitHub Issues](https://github.com/moment-team/link-window/issues)

---

**Moment-LinkWindow** - 让链接预览和文本操作更加智能便捷！
