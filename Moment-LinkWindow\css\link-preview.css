/* Moment-LinkWindow 链接预览样式 */
.moment-linkwindow-preview {
    position: fixed;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    user-select: none;
    z-index: 999999;
    min-width: 300px;
    min-height: 200px;
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .moment-linkwindow-preview {
        background: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }
    
    .preview-header {
        background: #3a3a3a !important;
        border-bottom-color: #404040 !important;
    }

    .preview-url-bar {
        background: rgba(64, 64, 64, 0.9) !important;
        border-color: #555 !important;
        color: #cccccc !important;
    }

    .preview-url-bar:hover {
        background: rgba(64, 64, 64, 1) !important;
        border-color: #777 !important;
    }

    .preview-url-bar:focus {
        border-color: #007bff !important;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25) !important;
    }

    .preview-loading {
        background: #2d2d2d !important;
    }
    
    .loading-text {
        color: #cccccc !important;
    }
}

/* 预览窗口头部 */
.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    cursor: move;
    min-height: 40px;
    gap: 10px;
}

/* URL地址栏样式 */
.preview-url-bar {
    flex: 1;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px 8px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
    color: #555;
    cursor: text;
    user-select: text;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    min-width: 0;
    max-width: 300px;
    transition: all 0.2s ease;
}

.preview-url-bar:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #999;
}

.preview-url-bar:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.preview-title {
    flex-shrink: 0;
    min-width: 0;
    font-weight: bold;
    color: #333;
}

.preview-controls {
    display: flex;
    gap: 5px;
}

.preview-controls button {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: background-color 0.2s;
    color: inherit;
}

.preview-controls button:hover {
    background: rgba(0, 0, 0, 0.1);
}

.preview-close:hover {
    background: #ff5f56;
    color: white;
}

.preview-pin:hover {
    background: #007bff;
    color: white;
}

.preview-minimize:hover {
    background: #ffc107;
    color: white;
}

/* 预览内容区域 */
.preview-content {
    flex: 1;
    position: relative;
    overflow: hidden;
    height: calc(100% - 40px);
}

.preview-content iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

/* 加载状态 */
.preview-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    z-index: 10;
    color: #666;
    font-size: 14px;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e0e0e0;
    border-top: 3px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

.loading-text {
    margin-top: 12px;
    font-size: 14px;
    color: #666;
}

.error-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.error-message {
    font-size: 14px;
    color: #d32f2f;
    text-align: center;
}

/* 通用旋转动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 链接预览加载指示器样式 */
.link-preview-loading-indicator {
    position: fixed;
    z-index: 10000;
    pointer-events: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.link-preview-loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid #1976d2;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

/* 调整大小手柄 */
.preview-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 15px;
    height: 15px;
    cursor: se-resize;
    background: rgba(0, 0, 0, 0.1);
    opacity: 0.6;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #666;
    border-top-left-radius: 4px;
    font-family: monospace;
    line-height: 1;
    user-select: none;
}

.preview-resize-handle:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.2);
    color: #333;
}

/* 最小化状态 */
.moment-linkwindow-preview.minimized {
    height: 40px !important;
    overflow: hidden;
}

.moment-linkwindow-preview.minimized .preview-content {
    display: none;
}

.moment-linkwindow-preview.minimized .preview-resize-handle {
    display: none;
}

/* 动画效果 */
.moment-linkwindow-preview.entering {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

.moment-linkwindow-preview.entered {
    opacity: 1;
    transform: scale(1) translateY(0);
}

.moment-linkwindow-preview.exiting {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

/* 响应式适配 */
@media (max-width: 768px) {
    .moment-linkwindow-preview {
        width: calc(100vw - 20px) !important;
        height: calc(100vh - 20px) !important;
        top: 10px !important;
        left: 10px !important;
        border-radius: 8px;
    }
    
    .preview-url-bar {
        max-width: 200px;
    }
}

/* 焦点状态 */
.moment-linkwindow-preview.focused {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #1976d2;
}

/* 拖拽状态 */
.moment-linkwindow-preview.dragging {
    transition: none !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    z-index: 1000000;
    backdrop-filter: none; /* 拖拽时禁用背景模糊效果 */
}

.moment-linkwindow-preview.dragging .preview-header {
    cursor: grabbing !important;
}

.moment-linkwindow-preview.resizing {
    transition: none !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    z-index: 1000000;
}

/* 在拖拽和调整大小时禁用iframe的指针事件，防止事件被iframe捕获 */
.moment-linkwindow-preview.dragging iframe,
.moment-linkwindow-preview.resizing iframe {
    pointer-events: none;
}

/* 拖拽和调整大小时的视觉反馈 */
.moment-linkwindow-preview.dragging {
    /* 移除缩放和透明度效果，只保留必要的拖拽状态 */
}

.moment-linkwindow-preview.resizing {
    /* 移除透明度效果，保持调整大小时的清晰显示 */
}

/* 确保拖拽手柄在调整大小时保持可见 */
.moment-linkwindow-preview.resizing .preview-resize-handle {
    opacity: 1;
    background: rgba(0, 0, 0, 0.3);
}

/* 🎯 修复：移除文本拖拽特殊样式，确保与链接弹窗完全一致 */
/* 文本拖拽弹窗现在使用与链接弹窗相同的样式和配置 */

/* 多窗口层叠效果 */
.moment-linkwindow-preview:nth-of-type(2) {
    transform: translateX(20px) translateY(20px);
}

.moment-linkwindow-preview:nth-of-type(3) {
    transform: translateX(40px) translateY(40px);
}
