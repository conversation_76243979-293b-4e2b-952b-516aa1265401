/**
 * Moment-Unified 侧边栏设置页面脚本
 * 支持标签管理和链接弹窗两个功能模块
 */

class UnifiedSidepanel {
    constructor() {
        this.linkWindowSettings = null;
        this.currentTab = 'tab-manager'; // 默认显示标签管理
        this.shortcutListeners = new Map();

        // 快捷键设置相关属性
        this.isListening = false;
        this.currentShortcut = '';
        this.currentEditingShortcutType = null;
        this.shortcutModalEventsBound = false;
        this.keydownHandler = null;

        this.init();
    }

    async init() {
        console.log('🚀 初始化 Unified 侧边栏');

        // 加载设置
        await this.loadAllSettings();

        // 绑定事件
        this.bindEvents();

        // 更新UI
        this.updateUI();

        // 初始化快捷键监听器
        this.registerNewShortcutListeners();

        console.log('✅ Unified 侧边栏初始化完成');
    }

    async loadAllSettings() {
        try {
            // 加载链接弹窗设置
            const linkResponse = await chrome.runtime.sendMessage({ type: 'getSettings' });
            if (linkResponse.success) {
                this.linkWindowSettings = linkResponse.settings || this.getDefaultLinkWindowSettings();
            } else {
                this.linkWindowSettings = this.getDefaultLinkWindowSettings();
            }

            console.log('📋 设置已加载');
        } catch (error) {
            console.error('❌ 加载设置失败:', error);
            this.linkWindowSettings = this.getDefaultLinkWindowSettings();
        }
    }

    getDefaultLinkWindowSettings() {
        return {
            linkPreview: {
                enabled: true,
                trigger: {
                    method: 'alt+click',
                    customKey: 'Alt',
                    delay: 300
                },
                window: {
                    size: 'medium',
                    position: 'center',
                    color: '#667eea',
                    background: 'default',
                    backgroundOpacity: 0.95
                },
                textActions: {
                    enabled: true,
                    directions: {
                        up: 'search',
                        down: 'translate',
                        left: 'search',
                        right: 'search'
                    },
                    searchEngine: 'baidu',
                    translateEngine: 'baidu',
                    targetLanguage: 'zh'
                }
            }
        };
    }



    bindEvents() {
        // 标签切换事件
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 标签管理事件
        this.bindTabManagerEvents();

        // 链接弹窗事件
        this.bindLinkWindowEvents();

        // 快捷键模态框事件
        this.bindShortcutModalEvents();
    }

    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容面板
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;
        console.log(`🔄 切换到 ${tabName} 面板`);
    }

    bindTabManagerEvents() {
        // 快速操作按钮
        document.getElementById('groupTabsBtn')?.addEventListener('click', () => {
            this.executeTabAction('groupTabs');
        });

        document.getElementById('ungroupTabsBtn')?.addEventListener('click', () => {
            this.executeTabAction('ungroupTabs');
        });

        document.getElementById('dedupeTabsBtn')?.addEventListener('click', () => {
            this.executeTabAction('deduplicateTabs');
        });

        document.getElementById('copyTabsBtn')?.addEventListener('click', () => {
            this.showCopyModal();
        });

        // 快捷键编辑按钮
        document.getElementById('groupShortcutEditBtn')?.addEventListener('click', () => {
            this.startShortcutEdit('group');
        });

        document.getElementById('dedupeShortcutEditBtn')?.addEventListener('click', () => {
            this.startShortcutEdit('dedupe');
        });

        document.getElementById('copyShortcutEditBtn')?.addEventListener('click', () => {
            this.startShortcutEdit('copy');
        });

        document.getElementById('ungroupShortcutEditBtn')?.addEventListener('click', () => {
            this.startShortcutEdit('ungroup');
        });

        // 快捷键设置现在使用模态框模式，不需要绑定内联编辑事件




    }

    bindShortcutActions() {
        // 已废弃，使用模态框模式
    }

    startShortcutEdit(type) {
        this.currentEditingShortcutType = type;
        this.showShortcutModal();
    }

    handleShortcutInput(e) {
        const keys = [];

        if (e.ctrlKey) keys.push('Ctrl');
        if (e.altKey) keys.push('Alt');
        if (e.shiftKey) keys.push('Shift');
        if (e.metaKey) keys.push('Meta');

        // 忽略单独的修饰键
        if (['Control', 'Shift', 'Alt', 'Meta'].includes(e.key)) {
            return;
        }

        // 添加主键
        if (e.key.length === 1 && /[A-Za-z0-9]/.test(e.key)) {
            keys.push(e.key.toUpperCase());
        } else {
            // 支持功能键和特殊键
            const specialKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12', 'Space', 'Enter', 'Tab', 'Escape'];
            if (specialKeys.includes(e.key)) {
                keys.push(e.key);
            }
        }

        if (keys.length >= 2) { // 至少需要一个修饰键和一个主键
            this.currentShortcut = keys.join('+');
            const displayEl = document.getElementById('shortcutDisplay');
            const confirmEl = document.getElementById('shortcutConfirm');

            if (displayEl) {
                displayEl.textContent = this.currentShortcut;
            }

            if (confirmEl) {
                confirmEl.disabled = false;
            }
        }
    }



    // 验证快捷键格式
    validateShortcut(shortcut) {
        console.log('🔍 验证快捷键格式:', shortcut);

        if (!shortcut || typeof shortcut !== 'string') {
            console.log('❌ 快捷键为空或不是字符串');
            return false;
        }

        const parts = shortcut.split('+').map(part => part.trim());
        console.log('📋 快捷键组成部分:', parts);

        // 检查是否包含修饰键
        const modifiers = ['Ctrl', 'Alt', 'Shift', 'Meta'];
        const hasModifier = parts.some(part => modifiers.includes(part));

        if (!hasModifier) {
            console.log('❌ 缺少修饰键');
            return false;
        }

        // 检查是否有主键
        const mainKey = parts.find(part => !modifiers.includes(part));
        if (!mainKey || mainKey.length === 0) {
            console.log('❌ 缺少主键');
            return false;
        }

        // 检查主键是否有效
        const validKeys = /^[A-Za-z0-9]$|^(F[1-9]|F1[0-2])$|^(Space|Enter|Tab|Escape|Delete|Backspace)$/;
        if (!validKeys.test(mainKey)) {
            console.log('❌ 主键无效:', mainKey);
            return false;
        }

        console.log('✅ 快捷键格式有效');
        return true;
    }

    // 格式化快捷键显示
    formatShortcut(shortcut) {
        if (!shortcut) return '';

        // 标准化快捷键格式
        return shortcut
            .split('+')
            .map(part => part.trim())
            .filter(part => part.length > 0)
            .join('+');
    }



    async updateShortcutDisplays() {
        try {
            // 从本地存储获取当前快捷键设置
            const result = await chrome.storage.local.get(['moment-tab-shortcuts']);
            const shortcuts = result['moment-tab-shortcuts'] || {
                group: 'Ctrl+M',
                dedupe: 'Ctrl+Shift+M',
                copy: 'Ctrl+K',
                ungroup: 'Ctrl+Shift+K'
            };

            // 更新显示
            const typeMapping = {
                group: 'groupTabs',
                dedupe: 'dedupeTabs',
                copy: 'copyTabs',
                ungroup: 'ungroupTabs'
            };

            ['group', 'dedupe', 'copy', 'ungroup'].forEach(type => {
                const textEl = document.getElementById(`${type}ShortcutText`);
                const btnShortcut = document.querySelector(`#${typeMapping[type]}Btn .btn-shortcut`);

                if (textEl) textEl.textContent = this.formatShortcut(shortcuts[type]);
                if (btnShortcut) btnShortcut.textContent = this.formatShortcut(shortcuts[type]);
            });
        } catch (error) {
            console.error('更新快捷键显示失败:', error);
        }
    }

    showCopyModal() {
        // 创建复制选项模态框
        const modal = document.createElement('div');
        modal.className = 'copy-modal-overlay';
        modal.innerHTML = `
            <div class="copy-modal">
                <h4>复制标签页</h4>
                <button class="copy-option-btn" data-action="copyCurrentTab">
                    <span class="btn-icon">📄</span>
                    复制当前页面
                </button>
                <button class="copy-option-btn" data-action="copyAllTabs">
                    <span class="btn-icon">📋</span>
                    复制所有页面
                </button>
                <button class="copy-cancel-btn">取消</button>
            </div>
        `;

        // 绑定事件
        modal.addEventListener('click', (e) => {
            if (e.target.classList.contains('copy-modal-overlay') || e.target.classList.contains('copy-cancel-btn')) {
                modal.remove();
            } else if (e.target.closest('.copy-option-btn')) {
                const action = e.target.closest('.copy-option-btn').dataset.action;
                this.executeTabAction(action);
                modal.remove();
            }
        });

        document.body.appendChild(modal);
    }





    updateUI() {
        // 根据当前标签更新UI
        if (this.currentTab === 'tab-manager') {
            this.updateTabManagerUI();
        } else if (this.currentTab === 'link-preview') {
            this.updateLinkWindowUI();
        }
    }

    updateTabManagerUI() {
        // 更新快捷键显示
        this.updateShortcutDisplays();
    }

    updateLinkWindowUI() {
        // 更新链接弹窗相关UI
        this.updateLinkWindowSettings();
    }

    updateLinkWindowSettings() {
        if (!this.linkWindowSettings) return;

        // 更新触发方式
        const triggerMethod = document.getElementById('triggerMethod');
        if (triggerMethod && this.linkWindowSettings.linkPreview?.trigger?.method) {
            triggerMethod.value = this.linkWindowSettings.linkPreview.trigger.method;
        }

        // 更新其他设置...
        // 这里可以添加更多LinkWindow设置的UI更新逻辑
    }

    bindLinkWindowEvents() {
        // 触发方式变化
        document.getElementById('triggerMethod')?.addEventListener('change', (e) => {
            this.updateTriggerMethod(e.target.value);
        });

        // 自定义快捷键设置
        document.getElementById('customKeyBtn')?.addEventListener('click', () => {
            this.showShortcutModal('linkWindow');
        });

        // 滑块事件
        document.getElementById('triggerDelay')?.addEventListener('input', async (e) => {
            await this.updateSliderValue(e.target, 'ms');
        });

        document.getElementById('backgroundOpacity')?.addEventListener('input', async (e) => {
            await this.updateSliderValue(e.target, '%', 100);
        });

        // 颜色选择器
        document.getElementById('windowColor')?.addEventListener('change', async (e) => {
            await this.updateLinkWindowSetting('linkPreview.window.color', e.target.value);
        });

        // 颜色预设
        document.querySelectorAll('.color-preset').forEach(preset => {
            preset.addEventListener('click', async () => {
                const color = preset.dataset.color;
                document.getElementById('windowColor').value = color;
                await this.updateSetting('linkPreview.window.color', color);
            });
        });

        // 其他选择器
        this.bindSelectEvents();

        // 操作按钮
        const resetBtn = document.getElementById('resetSettings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetSettings();
            });
        }

        // 快捷键模态框事件已在 bindEvents 中统一绑定
    }

    bindSelectEvents() {
        const selectMappings = {
            'windowSize': 'linkPreview.window.size',
            'windowPosition': 'linkPreview.window.position',
            'windowBackground': 'linkPreview.window.background',
            'textDragEnabled': 'linkPreview.textActions.enabled',
            'dragUp': 'linkPreview.textActions.directions.up',
            'dragDown': 'linkPreview.textActions.directions.down',
            'dragLeft': 'linkPreview.textActions.directions.left',
            'dragRight': 'linkPreview.textActions.directions.right',
            'searchEngine': 'linkPreview.textActions.searchEngine',
            'translateEngine': 'linkPreview.textActions.translateEngine',
            'targetLanguage': 'linkPreview.textActions.targetLanguage'
        };

        Object.entries(selectMappings).forEach(([elementId, settingPath]) => {
            const element = document.getElementById(elementId);
            if (element) {
                element.addEventListener('change', async (e) => {
                    let value = e.target.value;
                    if (settingPath === 'linkPreview.textActions.enabled') {
                        value = value === 'true';
                    }
                    await this.updateSetting(settingPath, value);
                });
            }
        });
    }

    bindShortcutModalEvents() {
        // 防止重复绑定
        if (this.shortcutModalEventsBound) {
            return;
        }

        const cancelBtn = document.getElementById('shortcutCancel');
        const confirmBtn = document.getElementById('shortcutConfirm');

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.hideShortcutModal();
            });
        }

        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.confirmShortcut();
            });
        }

        // 监听键盘事件
        this.keydownHandler = (e) => {
            if (this.isListening) {
                e.preventDefault();
                e.stopPropagation();
                this.handleShortcutInput(e);
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
        this.shortcutModalEventsBound = true;
    }

    async updateTriggerMethod(method) {
        this.updateSetting('linkPreview.trigger.method', method);

        // 更新UI显示
        const customKeyContainer = document.getElementById('customKeyContainer');
        const triggerDelayContainer = document.getElementById('triggerDelayContainer');

        const needsCustomKey = method.includes('alt+') || method.includes('自定义快捷键');
        const needsDelay = ['alt+hover', 'longpress', 'hover'].includes(method);

        customKeyContainer.style.display = needsCustomKey ? 'block' : 'none';

        const delaySlider = document.getElementById('triggerDelay');
        const delayDescription = triggerDelayContainer.querySelector('.delay-description');

        if (needsDelay) {
            delaySlider.disabled = false;
            delayDescription.textContent = this.getDelayDescription(method);
            triggerDelayContainer.classList.remove('disabled');
        } else {
            delaySlider.disabled = true;
            delayDescription.textContent = '当前触发方式不支持延迟设置';
            triggerDelayContainer.classList.add('disabled');
        }

        // 自动保存设置
        await this.saveSettings();
        console.log(`🔧 触发方式已更新: ${method}`);
    }

    getDelayDescription(method) {
        const descriptions = {
            'alt+hover': '调节悬停触发的延迟时间',
            'longpress': '调节长按触发的延迟时间',
            'hover': '调节悬停触发的延迟时间'
        };
        return descriptions[method] || '调节触发延迟时间';
    }

    async updateSliderValue(slider, unit, multiplier = 1) {
        const value = parseFloat(slider.value);
        const displayValue = Math.round(value * multiplier);
        const valueSpan = slider.parentNode.querySelector('.slider-value');
        valueSpan.textContent = `${displayValue}${unit}`;

        // 更新设置
        const settingPath = slider.id === 'triggerDelay' ?
            'linkPreview.trigger.delay' : 'linkPreview.window.backgroundOpacity';
        await this.updateSetting(settingPath, value);
    }

    async updateSetting(path, value) {
        const keys = path.split('.');
        let current = this.linkWindowSettings;

        for (let i = 0; i < keys.length - 1; i++) {
            if (!current[keys[i]]) {
                current[keys[i]] = {};
            }
            current = current[keys[i]];
        }

        current[keys[keys.length - 1]] = value;
        console.log(`🔧 设置已更新并保存: ${path} = ${value}`);

        // 自动保存设置
        await this.saveSettings();
    }

    updateUI() {
        if (!this.linkWindowSettings) return;

        const { linkPreview } = this.linkWindowSettings;

        // 更新基础设置
        this.setSelectValue('triggerMethod', linkPreview.trigger.method);
        this.updateTriggerMethod(linkPreview.trigger.method);
        
        document.getElementById('customKey').value = linkPreview.trigger.customKey || 'Alt';
        
        const delaySlider = document.getElementById('triggerDelay');
        delaySlider.value = linkPreview.trigger.delay || 300;
        this.updateSliderValue(delaySlider, 'ms');
        
        this.setSelectValue('windowSize', linkPreview.window.size);
        this.setSelectValue('windowPosition', linkPreview.window.position);
        
        document.getElementById('windowColor').value = linkPreview.window.color || '#667eea';
        
        const opacitySlider = document.getElementById('backgroundOpacity');
        opacitySlider.value = linkPreview.window.backgroundOpacity || 0.95;
        this.updateSliderValue(opacitySlider, '%', 100);
        
        this.setSelectValue('windowBackground', linkPreview.window.background);

        // 更新文本拖拽设置
        this.setSelectValue('textDragEnabled', linkPreview.textActions.enabled ? 'true' : 'false');
        this.setSelectValue('dragUp', linkPreview.textActions.directions.up);
        this.setSelectValue('dragDown', linkPreview.textActions.directions.down);
        this.setSelectValue('dragLeft', linkPreview.textActions.directions.left);
        this.setSelectValue('dragRight', linkPreview.textActions.directions.right);
        this.setSelectValue('searchEngine', linkPreview.textActions.searchEngine);
        this.setSelectValue('translateEngine', linkPreview.textActions.translateEngine);
        this.setSelectValue('targetLanguage', linkPreview.textActions.targetLanguage);
    }

    setSelectValue(elementId, value) {
        const element = document.getElementById(elementId);
        if (element && value !== undefined) {
            element.value = value;
        }
    }

    showShortcutModal() {
        const modal = document.getElementById('shortcutModal');
        if (!modal) return;

        modal.classList.remove('hidden');
        this.isListening = true;
        this.currentShortcut = '';

        const displayEl = document.getElementById('shortcutDisplay');
        const confirmEl = document.getElementById('shortcutConfirm');

        if (displayEl) {
            displayEl.textContent = '等待输入...';
        }

        if (confirmEl) {
            confirmEl.disabled = true;
        }
    }

    hideShortcutModal() {
        const modal = document.getElementById('shortcutModal');
        modal.classList.add('hidden');
        this.isListening = false;
        this.currentShortcut = '';
    }

    // 移除旧的快捷键监听器
    removeOldShortcutListeners() {
        this.shortcutListeners.forEach((listener, key) => {
            document.removeEventListener('keydown', listener);
            console.log(`🗑️ 已移除快捷键监听器: ${key}`);
        });
        this.shortcutListeners.clear();
    }



    // 注册新的快捷键监听器
    registerNewShortcutListeners() {
        // 注册链接弹窗快捷键
        this.registerLinkPreviewShortcuts();


    }

    // 注册链接弹窗快捷键
    registerLinkPreviewShortcuts() {
        if (!this.linkWindowSettings || !this.linkWindowSettings.linkPreview) {
            console.warn('链接弹窗设置未加载，跳过快捷键注册');
            return;
        }

        const triggerMethod = this.linkWindowSettings.linkPreview.trigger.method;
        const customKey = this.linkWindowSettings.linkPreview.trigger.customKey;

        // 只有在使用自定义快捷键的触发方式时才注册监听器
        if (triggerMethod.includes('alt+') || triggerMethod.includes('自定义快捷键')) {
            const listenerKey = `linkPreview_${triggerMethod}_${customKey}`;

            const listener = (e) => {
                // 检查是否按下了正确的修饰键
                const isCorrectKey = this.isCustomKeyPressed(e, customKey);
                if (isCorrectKey) {
                    console.log(`⌨️ 检测到链接弹窗快捷键: ${customKey}`);
                    // 这里可以添加快捷键触发的逻辑
                }
            };

            document.addEventListener('keydown', listener);
            this.shortcutListeners.set(listenerKey, listener);
        }
    }





    // 检查是否按下了指定的修饰键
    isCustomKeyPressed(event, customKey) {
        switch (customKey) {
            case 'Alt':
                return event.altKey && !event.ctrlKey && !event.shiftKey && !event.metaKey;
            case 'Ctrl':
                return event.ctrlKey && !event.altKey && !event.shiftKey && !event.metaKey;
            case 'Shift':
                return event.shiftKey && !event.altKey && !event.ctrlKey && !event.metaKey;
            case 'Meta':
                return event.metaKey && !event.altKey && !event.ctrlKey && !event.shiftKey;
            default:
                return event.altKey && !event.ctrlKey && !event.shiftKey && !event.metaKey;
        }
    }

    // 错误的 handleShortcutInput 方法已删除，使用正确的实现

    async confirmShortcut() {
        if (this.currentShortcut) {
            // 检查是否是标签管理快捷键设置
            if (this.currentEditingShortcutType) {
                // 标签管理快捷键设置
                await this.saveTabManagerShortcut(this.currentEditingShortcutType, this.currentShortcut);
                this.currentEditingShortcutType = null;
            } else {
                // 链接弹窗快捷键设置
                this.removeOldShortcutListeners();
                this.updateSetting('linkPreview.trigger.customKey', this.currentShortcut);
                document.getElementById('customKey').value = this.currentShortcut;
                await this.saveSettings();
                this.registerNewShortcutListeners();
                this.showStatusMessage('快捷键已更新为: ' + this.currentShortcut, 'success');
            }
        }
        this.hideShortcutModal();
    }

    async saveTabManagerShortcut(type, shortcut) {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'updateShortcuts',
                shortcuts: { [type]: shortcut }
            });

            if (response && response.success) {
                await this.updateShortcutDisplays();
                const actionNames = {
                    group: '分组',
                    dedupe: '去重',
                    copy: '复制',
                    ungroup: '取消分组'
                };
                const actionName = actionNames[type] || '未知操作';
                this.showStatusMessage(`${actionName}快捷键已更新为 ${shortcut}`, 'success');
            } else {
                this.showStatusMessage('保存快捷键失败：' + (response?.error || '未知错误'), 'error');
            }
        } catch (error) {
            this.showStatusMessage('保存快捷键失败：' + error.message, 'error');
        }
    }

    async resetSettings() {
        if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
            this.linkWindowSettings = this.getDefaultLinkWindowSettings();
            this.updateUI();
            await this.saveSettings();
            this.showStatusMessage('设置已重置', 'success');
        }
    }

    async saveSettings() {
        try {
            const response = await chrome.runtime.sendMessage({
                type: 'updateSettings',
                settings: this.linkWindowSettings
            });

            if (response.success) {
                return true;
            } else {
                console.error('❌ 设置保存失败:', response.error);
                this.showStatusMessage('保存失败: ' + response.error, 'error');
                return false;
            }
        } catch (error) {
            console.error('❌ 保存设置失败:', error);
            this.showStatusMessage('保存失败: ' + error.message, 'error');
            return false;
        }
    }

    showStatusMessage(message, type = 'info') {
        const statusEl = document.getElementById('statusMessage');
        statusEl.textContent = message;
        statusEl.className = `status-message ${type}`;
        statusEl.classList.remove('hidden');
        
        setTimeout(() => {
            statusEl.classList.add('hidden');
        }, 3000);
    }
    // ===== 标签管理相关方法 =====

    async executeTabAction(action) {
        console.log(`🔥 sidepanel executeTabAction 被调用，action: ${action}`);
        try {
            console.log(`📤 发送消息到 background script:`, { action });
            const response = await chrome.runtime.sendMessage({ action });
            console.log(`📥 收到 background script 响应:`, response);

            if (response.success) {
                let message = `${this.getActionName(action)}成功`;

                // 添加详细信息
                if (response.result) {
                    if (response.result.groupCount !== undefined) {
                        message = `成功创建了 ${response.result.groupCount} 个分组`;
                    } else if (response.result.ungroupCount !== undefined) {
                        message = `成功取消了 ${response.result.ungroupCount} 个分组`;
                    } else if (response.result.count !== undefined) {
                        message = `成功复制了 ${response.result.count} 个标签页`;
                    } else if (response.result.title) {
                        message = `成功复制: ${response.result.title}`;
                    } else if (response.result.message) {
                        message = response.result.message;
                    }
                }

                console.log(`✅ 显示成功消息: ${message}`);
                this.showStatusMessage(message, 'success');


            } else {
                const errorMessage = `${this.getActionName(action)}失败: ${response.error || '未知错误'}`;
                console.error(`❌ 操作失败: ${errorMessage}`);
                this.showStatusMessage(errorMessage, 'error');
            }
        } catch (error) {
            console.error(`❌ 执行${action}失败:`, error);
            this.showStatusMessage(`${this.getActionName(action)}失败`, 'error');
        }
    }

    getActionName(action) {
        const actionNames = {
            'groupTabs': '标签分组',
            'ungroupTabs': '取消分组',
            'deduplicateTabs': '标签去重',
            'copyCurrentTab': '复制当前页面',
            'copyAllTabs': '复制所有页面'
        };
        return actionNames[action] || action;
    }



    // ===== 链接弹窗相关方法 =====

    async updateLinkWindowSetting(path, value) {
        try {
            const keys = path.split('.');
            let current = this.linkWindowSettings;

            for (let i = 0; i < keys.length - 1; i++) {
                if (!current[keys[i]]) {
                    current[keys[i]] = {};
                }
                current = current[keys[i]];
            }

            current[keys[keys.length - 1]] = value;
            console.log(`🔧 LinkWindow设置已更新: ${path} = ${value}`);

            // 保存设置
            await this.saveLinkWindowSettings();
        } catch (error) {
            console.error('❌ 更新LinkWindow设置失败:', error);
        }
    }

    async saveLinkWindowSettings() {
        try {
            const response = await chrome.runtime.sendMessage({
                type: 'updateSettings',
                settings: this.linkWindowSettings
            });

            if (response.success) {
                console.log('✅ LinkWindow设置已保存');
                return true;
            } else {
                console.error('❌ LinkWindow设置保存失败:', response.error);
                this.showStatusMessage('保存失败: ' + response.error, 'error');
                return false;
            }
        } catch (error) {
            console.error('❌ 保存LinkWindow设置失败:', error);
            this.showStatusMessage('保存失败', 'error');
            return false;
        }
    }

    // ===== 通用方法 =====

    updateSliderDisplay(slider) {
        const valueSpan = slider.parentElement.querySelector('.slider-value');
        if (valueSpan) {
            valueSpan.textContent = slider.value;
        }
    }

    showStatusMessage(message, type = 'info') {
        const statusEl = document.getElementById('statusMessage');
        if (statusEl) {
            statusEl.textContent = message;
            statusEl.className = `status-message ${type}`;
            statusEl.classList.remove('hidden');

            setTimeout(() => {
                statusEl.classList.add('hidden');
            }, 3000);
        }
    }
}

// 初始化侧边栏
document.addEventListener('DOMContentLoaded', () => {
    new UnifiedSidepanel();
});
