<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨标签页窗口尺寸继承测试 - Moment-LinkWindow</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .status-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e1e5e9;
        }

        .status-ok {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .status-info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        .status-warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .size-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 16px;
        }

        .size-display h3 {
            margin-top: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .test-link {
            display: block;
            padding: 15px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .test-link:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        .test-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .test-steps h3 {
            color: #856404;
            margin-top: 0;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }

        .step-number {
            background: #007bff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .step-description {
            flex: 1;
        }

        .highlight-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .highlight-box h3 {
            color: #856404;
            margin-top: 0;
        }

        .cross-tab-indicator {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .cross-tab-indicator h3 {
            color: #007bff;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📏 跨标签页窗口尺寸继承测试</h1>

        <div class="cross-tab-indicator">
            <h3>🔍 环境检测</h3>
            <p id="environment-status">正在检测当前环境...</p>
        </div>

        <div class="size-display">
            <h3>📐 当前窗口尺寸信息</h3>
            <div id="size-info">正在获取窗口尺寸信息...</div>
        </div>

        <div class="status-section status-info">
            <h3>📋 测试目标</h3>
            <p><strong>验证跨标签页窗口尺寸继承：</strong>确保在跨标签页窗口中触发的新链接弹窗与原窗口具有完全相同的尺寸（宽度和高度）</p>
        </div>

        <div class="highlight-box">
            <h3>🎯 修复的问题</h3>
            <p><strong>修复前问题：</strong>新创建的跨标签页窗口使用默认尺寸（800x600），与原窗口尺寸不一致</p>
            <p><strong>修复后行为：</strong>新创建的跨标签页窗口继承原窗口的实际尺寸（window.outerWidth x window.outerHeight）</p>
        </div>

        <div class="test-steps">
            <h3>🧪 详细测试步骤</h3>
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-description">记录当前窗口的尺寸信息（如上方显示）</div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-description">在普通页面中创建弹窗，然后升级为跨标签页窗口</div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-description">调整跨标签页窗口到特定尺寸（如拖拽边框改变大小）</div>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-description">在跨标签页窗口中按住Alt键点击下方任意链接</div>
            </div>
            <div class="step">
                <div class="step-number">5</div>
                <div class="step-description">🎯 验证：新窗口的尺寸应该与当前窗口完全一致</div>
            </div>
            <div class="step">
                <div class="step-number">6</div>
                <div class="step-description">比较两个窗口的宽度和高度，确认尺寸继承正确</div>
            </div>
        </div>

        <div class="status-section status-warning">
            <h3>⚠️ 测试注意事项</h3>
            <p><strong>尺寸对比：</strong>新窗口的尺寸应该与当前窗口的 outerWidth 和 outerHeight 完全一致，包括窗口边框和标题栏</p>
            <p><strong>位置偏移：</strong>新窗口位置会有轻微偏移以避免完全重叠，但尺寸应该完全相同</p>
        </div>

        <div class="status-section status-info">
            <h3>🔗 测试链接</h3>
            <p>点击这些链接测试尺寸继承功能（记得按住Alt键）：</p>
            <div class="test-links">
                <a href="https://www.baidu.com" class="test-link">百度首页</a>
                <a href="https://www.github.com" class="test-link">GitHub</a>
                <a href="https://www.google.com" class="test-link">Google</a>
                <a href="https://www.stackoverflow.com" class="test-link">Stack Overflow</a>
                <a href="https://news.ycombinator.com" class="test-link">Hacker News</a>
                <a href="https://www.reddit.com" class="test-link">Reddit</a>
            </div>
        </div>

        <div class="status-section status-info">
            <h3>🧪 模拟测试</h3>
            <p>点击下方按钮模拟跨标签页窗口中的尺寸计算逻辑：</p>
            <button onclick="simulateSizeCalculation()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; margin: 10px 0;">
                🔧 模拟尺寸计算
            </button>
            <div id="simulation-result" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: 'Courier New', monospace; display: none;"></div>
        </div>

        <div class="status-section status-ok">
            <h3>✅ 验证清单</h3>
            <ul>
                <li><strong>环境检测：</strong>正确识别跨标签页窗口环境</li>
                <li><strong>尺寸获取：</strong>正确获取当前窗口的 outerWidth 和 outerHeight</li>
                <li><strong>尺寸继承：</strong>新窗口宽度与原窗口宽度完全一致</li>
                <li><strong>高度继承：</strong>新窗口高度与原窗口高度完全一致</li>
                <li><strong>位置计算：</strong>新窗口位置基于点击位置智能计算</li>
                <li><strong>边界检查：</strong>新窗口不会超出屏幕边界</li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📏 跨标签页窗口尺寸继承测试页面已加载');
            
            // 检测环境
            checkEnvironment();
            
            // 显示当前窗口尺寸信息
            displayWindowSizeInfo();
            
            // 定期更新尺寸信息（窗口可能被调整）
            setInterval(displayWindowSizeInfo, 2000);
        });

        function checkEnvironment() {
            const statusElement = document.getElementById('environment-status');
            const urlParams = new URLSearchParams(window.location.search);
            const isCrossTabWindow = urlParams.has('__moment_linkwindow_cross_tab');
            
            if (isCrossTabWindow) {
                statusElement.innerHTML = '✅ <strong>当前在跨标签页窗口中</strong> - 可以测试尺寸继承功能';
                statusElement.style.color = '#155724';
                console.log('✅ 检测到跨标签页窗口环境');
            } else {
                statusElement.innerHTML = '📄 <strong>当前在普通页面中</strong> - 请先创建弹窗并升级为跨标签页窗口';
                statusElement.style.color = '#856404';
                console.log('📄 当前在普通页面环境');
            }
        }

        function displayWindowSizeInfo() {
            const sizeInfo = document.getElementById('size-info');
            
            const info = {
                'outerWidth (包含边框)': window.outerWidth + 'px',
                'outerHeight (包含边框)': window.outerHeight + 'px',
                'innerWidth (内容区域)': window.innerWidth + 'px',
                'innerHeight (内容区域)': window.innerHeight + 'px',
                'screenX (屏幕X坐标)': window.screenX + 'px',
                'screenY (屏幕Y坐标)': window.screenY + 'px',
                '屏幕分辨率': screen.width + 'x' + screen.height
            };

            let html = '';
            for (const [key, value] of Object.entries(info)) {
                html += `<strong>${key}:</strong> ${value}<br>`;
            }

            sizeInfo.innerHTML = html;
            
            // 记录到控制台
            console.log('📐 当前窗口尺寸:', {
                outerWidth: window.outerWidth,
                outerHeight: window.outerHeight,
                innerWidth: window.innerWidth,
                innerHeight: window.innerHeight,
                screenX: window.screenX,
                screenY: window.screenY
            });
        }

        function simulateSizeCalculation() {
            // 模拟 calculateCrossTabWindowPosition 方法的尺寸计算逻辑
            const currentWidth = window.outerWidth || 800;
            const currentHeight = window.outerHeight || 600;

            const windowOptions = {
                width: currentWidth,
                height: currentHeight,
                left: window.screenX + 50,
                top: window.screenY + 50
            };

            // 边界检查
            const maxLeft = screen.width - windowOptions.width - 50;
            const maxTop = screen.height - windowOptions.height - 50;

            windowOptions.left = Math.min(windowOptions.left, maxLeft);
            windowOptions.top = Math.min(windowOptions.top, maxTop);

            const resultDiv = document.getElementById('simulation-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `
                <strong>🎯 修复后的尺寸计算结果：</strong><br>
                <strong>继承的宽度：</strong> ${windowOptions.width}px (来自 window.outerWidth)<br>
                <strong>继承的高度：</strong> ${windowOptions.height}px (来自 window.outerHeight)<br>
                <strong>计算的位置：</strong> (${windowOptions.left}, ${windowOptions.top})<br>
                <strong>对比修复前：</strong> 修复前使用固定的 800x600，现在使用实际的 ${currentWidth}x${currentHeight}<br>
                <strong>尺寸匹配：</strong> ✅ 新窗口尺寸与当前窗口完全一致
            `;

            console.log('🧪 模拟尺寸计算结果:', windowOptions);
            console.log('📊 修复对比:', {
                '修复前': '800x600 (固定默认值)',
                '修复后': `${currentWidth}x${currentHeight} (继承实际尺寸)`,
                '匹配状态': '✅ 完全一致'
            });
        }
    </script>
</body>
</html>
