/**
 * 标签管理器核心类
 * 负责所有标签页管理功能的实现
 */
class TabManager {
    constructor() {
        this.settings = {};
        this.isInitialized = false;
        
        // 快捷键触发防重复机制
        this.lastTriggerTime = 0;
        this.triggerCooldown = 100; // 100ms内不允许重复触发
        
        this.init();
    }

    async init() {
        try {
            // 加载设置
            await this.loadSettings();
            this.isInitialized = true;
            console.log('✅ 标签管理器初始化完成');
        } catch (error) {
            console.error('❌ 标签管理器初始化失败:', error);
        }
    }

    async loadSettings() {
        try {
            this.settings = await StorageUtils.loadSettings();
            console.log('📋 已加载设置:', this.settings);
        } catch (error) {
            console.warn('⚠️ 加载设置失败，使用默认设置:', error);
            this.settings = StorageUtils.getDefaultSettings();
        }
    }

    async saveSettings() {
        try {
            await StorageUtils.saveSettings(this.settings);
            console.log('✅ 设置已保存');
        } catch (error) {
            console.error('❌ 保存设置失败:', error);
        }
    }

    /**
     * 按域名分组标签页
     */
    async groupTabsByDomain() {
        // 防重复触发检测
        const now = Date.now();
        if (now - this.lastTriggerTime < this.triggerCooldown) {
            console.log('🚫 快捷键触发过于频繁，忽略重复触发');
            return;
        }
        this.lastTriggerTime = now;

        if (!this.isInitialized || !this.settings.enabled) {
            console.warn('标签分组功能未启用或未初始化');
            return;
        }

        try {
            console.log('🔄 开始执行标签分组...');

            // 获取所有标签页
            const tabs = await chrome.tabs.query({});
            
            // 按域名分组
            const domainGroups = this.groupTabsByDomainLogic(tabs);
            
            if (Object.keys(domainGroups).length === 0) {
                console.log('没有找到可分组的标签页');
                return;
            }

            // 创建标签分组
            await this.createTabGroups(domainGroups);
            console.log('✅ 标签分组完成');
        } catch (error) {
            console.error('❌ 标签分组失败:', error);
        }
    }

    /**
     * 标签页分组逻辑
     */
    groupTabsByDomainLogic(tabs) {
        const groups = {};

        tabs.forEach(tab => {
            if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                return;
            }

            try {
                const domain = DomainUtils.getDomainDisplayName(tab.url);
                
                if (!groups[domain]) {
                    groups[domain] = [];
                }
                groups[domain].push(tab);
            } catch (error) {
                console.warn('解析标签页域名失败:', tab.url, error);
            }
        });

        // 过滤掉只有一个标签页的域名（根据设置）
        if (this.settings.grouping.minTabsToGroup > 1) {
            Object.keys(groups).forEach(domain => {
                if (groups[domain].length < this.settings.grouping.minTabsToGroup) {
                    delete groups[domain];
                }
            });
        }

        return groups;
    }

    /**
     * 创建标签分组
     */
    async createTabGroups(domainGroups) {
        const colors = ['blue', 'red', 'yellow', 'green', 'pink', 'purple', 'cyan', 'orange'];
        let colorIndex = 0;

        for (const [domain, tabs] of Object.entries(domainGroups)) {
            try {
                // 获取标签页ID
                const tabIds = tabs.map(tab => tab.id);
                
                // 创建分组
                const group = await chrome.tabGroups.group({ tabIds });
                
                // 设置分组属性
                await chrome.tabGroups.update(group, {
                    title: domain,
                    color: colors[colorIndex % colors.length],
                    collapsed: this.settings.grouping.autoCollapse
                });

                colorIndex++;
                console.log(`📁 已创建分组: ${domain} (${tabs.length}个标签页)`);
            } catch (error) {
                console.error(`创建分组失败: ${domain}`, error);
            }
        }
    }

    /**
     * 去重标签页
     */
    async deduplicateTabs() {
        if (!this.isInitialized || !this.settings.enabled) {
            console.warn('标签去重功能未启用或未初始化');
            return;
        }

        try {
            console.log('🔄 开始执行标签页去重...');

            // 获取所有标签页
            const tabs = await chrome.tabs.query({});
            
            // 查找重复标签页
            const duplicates = this.findDuplicateTabs(tabs);
            
            if (duplicates.length === 0) {
                console.log('没有找到重复的标签页');
                return;
            }

            // 关闭重复标签页
            await this.closeDuplicateTabs(duplicates);
            console.log(`✅ 标签页去重完成，关闭了 ${duplicates.length} 个重复标签页`);
        } catch (error) {
            console.error('❌ 标签页去重失败:', error);
        }
    }

    /**
     * 查找重复标签页
     */
    findDuplicateTabs(tabs) {
        const urlMap = new Map();
        const duplicates = [];

        tabs.forEach(tab => {
            if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                return;
            }

            // 根据设置决定是否忽略URL参数
            let compareUrl = tab.url;
            if (this.settings.deduplication.ignoreUrlParams) {
                try {
                    const url = new URL(tab.url);
                    compareUrl = `${url.protocol}//${url.host}${url.pathname}`;
                } catch (error) {
                    // 如果URL解析失败，使用原始URL
                    compareUrl = tab.url;
                }
            }

            if (urlMap.has(compareUrl)) {
                // 找到重复，决定保留哪个
                const existingTab = urlMap.get(compareUrl);
                const tabToClose = this.decideDuplicateTab(existingTab, tab);
                duplicates.push(tabToClose);
            } else {
                urlMap.set(compareUrl, tab);
            }
        });

        return duplicates;
    }

    /**
     * 决定关闭哪个重复标签页
     */
    decideDuplicateTab(tab1, tab2) {
        // 优先保留活跃标签页
        if (tab1.active) return tab2;
        if (tab2.active) return tab1;

        // 优先保留固定标签页
        if (tab1.pinned && !tab2.pinned) return tab2;
        if (tab2.pinned && !tab1.pinned) return tab1;

        // 根据设置决定保留策略
        switch (this.settings.deduplication.keepStrategy) {
            case 'newest':
                return tab1.id < tab2.id ? tab1 : tab2;
            case 'oldest':
                return tab1.id > tab2.id ? tab1 : tab2;
            case 'leftmost':
                return tab1.index > tab2.index ? tab1 : tab2;
            case 'rightmost':
                return tab1.index < tab2.index ? tab1 : tab2;
            default:
                return tab2; // 默认保留第一个
        }
    }

    /**
     * 关闭重复标签页
     */
    async closeDuplicateTabs(duplicates) {
        const tabIds = duplicates.map(tab => tab.id);
        
        if (tabIds.length > 0) {
            await chrome.tabs.remove(tabIds);
        }
    }

    /**
     * 复制当前标签页
     */
    async copyCurrentTab() {
        try {
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (currentTab && currentTab.url) {
                await navigator.clipboard.writeText(currentTab.url);
                console.log('✅ 已复制当前标签页URL:', currentTab.url);
                return { success: true, url: currentTab.url };
            }
        } catch (error) {
            console.error('❌ 复制当前标签页失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 复制所有标签页
     */
    async copyAllTabs() {
        try {
            const tabs = await chrome.tabs.query({ currentWindow: true });
            const urls = tabs
                .filter(tab => tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://'))
                .map(tab => tab.url);
            
            if (urls.length > 0) {
                const urlText = urls.join('\n');
                await navigator.clipboard.writeText(urlText);
                console.log(`✅ 已复制 ${urls.length} 个标签页URL`);
                return { success: true, count: urls.length };
            }
        } catch (error) {
            console.error('❌ 复制所有标签页失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取用户快捷键设置
     */
    getUserShortcuts() {
        return this.settings.shortcuts || {
            group: 'Ctrl+M',
            dedupe: 'Ctrl+Shift+M',
            copy: 'Ctrl+K'
        };
    }

    /**
     * 更新用户快捷键设置
     */
    async updateUserShortcuts(shortcuts) {
        this.settings.shortcuts = shortcuts;
        await this.saveSettings();
        
        // 通知content script更新快捷键
        const tabs = await chrome.tabs.query({});
        for (const tab of tabs) {
            try {
                await chrome.tabs.sendMessage(tab.id, {
                    type: 'shortcutsUpdated',
                    shortcuts: shortcuts
                });
            } catch (error) {
                // 忽略无法发送消息的标签页
            }
        }
    }
}
