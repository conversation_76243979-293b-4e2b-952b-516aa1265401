/**
 * Moment-LinkWindow Content Script
 * 处理页面中的链接预览和文本拖拽功能
 */

// 统一日志管理器
class LinkWindowLogger {
    static log(type, message, data = null) {
        const prefix = this.getPrefix(type);

        if (data) {
            console.log(`${prefix} ${message}`, data);
        } else {
            console.log(`${prefix} ${message}`);
        }
    }

    static getPrefix(type) {
        const prefixes = {
            success: '✅',
            config: '🔧',
            memory: '🧠',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            window: '🪟',
            drag: '🖱️'
        };
        return prefixes[type] || 'ℹ️';
    }

    static success(message, data = null) { this.log('success', message, data); }
    static config(message, data = null) { this.log('config', message, data); }
    static memory(message, data = null) { this.log('memory', message, data); }
    static error(message, data = null) { this.log('error', message, data); }
    static warning(message, data = null) { this.log('warning', message, data); }
    static info(message, data = null) { this.log('info', message, data); }
    static window(message, data = null) { this.log('window', message, data); }
    static drag(message, data = null) { this.log('drag', message, data); }
}

// 统一常量定义
const LinkWindowConstants = {
    MOUSE_RANGE_LIMIT: 100,        // 鼠标有效范围限制
    SAVE_DELAY: 300,               // 延迟保存时间(ms)
    MIN_WINDOW_WIDTH: 300,         // 最小窗口宽度
    MIN_WINDOW_HEIGHT: 200,        // 最小窗口高度
    WINDOW_MARGIN: 10,             // 窗口边距
    MAX_WINDOWS: 5,                // 最大窗口数量
    DEFAULT_DELAY: 300,            // 默认延迟时间
    Z_INDEX_BASE: 10000           // z-index基础值
};

// 工具类
class LinkWindowUtils {
    static isChromeApiAvailable() {
        return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
    }

    static isExtensionEnvironment() {
        return this.isChromeApiAvailable();
    }

    static logError(context, error, ...args) {
        console.error(`❌ ${context}:`, error, ...args);
    }

    static logWarning(context, message, ...args) {
        console.warn(`⚠️ ${context}:`, message, ...args);
    }

    static logSuccess(context, message, ...args) {
        console.log(`✅ ${context}:`, message, ...args);
    }

    static logInfo(context, message, ...args) {
        console.log(`🔧 ${context}:`, message, ...args);
    }

    static safeRemoveElement(element) {
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    }

    static extractDomain(url) {
        try {
            return new URL(url).hostname;
        } catch {
            return url;
        }
    }

    static formatUrlForDisplay(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + urlObj.pathname;
        } catch {
            return url;
        }
    }
}

// 文本拖拽管理器
class LinkWindowTextDragManager {
    constructor() {
        this.config = TextDragUtils.getDefaultConfig();
        this.isDragging = false;
        this.startPosition = { x: 0, y: 0 };
        this.endPosition = { x: 0, y: 0 };
        this.selectedText = '';
        this.dragStartTime = 0;
        this.lastMousePosition = { x: 0, y: 0 };

        this.init();
    }

    async init() {
        await this.loadConfig();
        this.bindEvents();
        LinkWindowLogger.success('文本拖拽管理器已启用');
    }

    async loadConfig() {
        try {
            if (LinkWindowUtils.isChromeApiAvailable()) {
                const response = await chrome.runtime.sendMessage({ type: 'getSettings' });
                if (response.success && response.settings?.linkPreview?.textActions) {
                    this.config = { ...this.config, ...response.settings.linkPreview.textActions };
                    LinkWindowLogger.config('文本拖拽配置已加载', this.config);
                }
            }
        } catch (error) {
            console.warn('⚠️ 加载文本拖拽配置失败，使用默认配置:', error);
        }
    }

    bindEvents() {
        document.addEventListener('mousedown', this.handleMouseDown.bind(this), true);
        document.addEventListener('mousemove', this.handleMouseMove.bind(this), true);
        document.addEventListener('mouseup', this.handleMouseUp.bind(this), true);
    }

    handleMouseDown(e) {
        if (!this.config?.enabled) return;
        if (e.button !== 0) return; // 只处理左键
        
        const selection = window.getSelection();
        if (!selection || selection.isCollapsed) return;
        
        const selectedText = selection.toString().trim();
        if (!selectedText || selectedText.length < 1) return;
        
        if (!TextDragUtils.isClickInSelection(e, selection)) return;
        
        this.isDragging = true;
        this.startPosition = { x: e.clientX, y: e.clientY };
        this.selectedText = selectedText;
        this.dragStartTime = Date.now();
        
        e.preventDefault();
        console.log('🖱️ 开始文本拖拽:', selectedText.substring(0, 50));
    }

    handleMouseMove(e) {
        this.lastMousePosition = { x: e.clientX, y: e.clientY };
        
        if (this.isDragging) {
            this.endPosition = { x: e.clientX, y: e.clientY };
            document.body.style.cursor = 'grabbing';
        }
    }

    handleMouseUp(e) {
        if (!this.isDragging) return;
        
        this.isDragging = false;
        document.body.style.cursor = '';
        
        const distance = TextDragUtils.calculateDistance(this.startPosition, this.endPosition);
        const dragTime = Date.now() - this.dragStartTime;
        
        if (!TextDragUtils.validateDragConditions(distance, dragTime)) {
            console.log('🚫 拖拽条件不满足，忽略操作');
            return;
        }
        
        const direction = TextDragUtils.calculateDirection(this.startPosition, this.endPosition);
        console.log(`🎯 检测到拖拽方向: ${direction}, 距离: ${distance.toFixed(0)}px`);
        
        this.executeAction(direction, this.selectedText);
    }

    executeAction(direction, text) {
        const action = this.config.directions[direction];
        
        if (!TextDragUtils.shouldExecuteAction(action)) {
            console.log(`🚫 方向 ${direction} 未配置有效动作`);
            return;
        }

        console.log(`🚀 执行动作: ${action} (方向: ${direction}, 文本: "${text}")`);

        try {
            let url, title;

            if (action === 'search') {
                const result = TextDragUtils.buildSearchUrl(text, this.config);
                url = result.url;
                title = result.title;
            } else if (action === 'translate') {
                const result = TextDragUtils.buildTranslateUrl(text, this.config);
                url = result.url;
                title = result.title;
            }

            if (url) {
                // 创建自定义事件，通知链接预览管理器
                const event = new CustomEvent('textDragPreview', {
                    detail: {
                        url,
                        title,
                        text,
                        action,
                        direction,
                        position: this.lastMousePosition
                    }
                });

                document.dispatchEvent(event);
                LinkWindowLogger.success(`文本拖拽预览事件已发送: ${title}`);
            }
        } catch (error) {
            console.error('❌ 执行文本拖拽动作失败:', error);
        }
    }
}

// 加载指示器管理器
class LinkWindowLoadingIndicator {
    constructor() {
        this.indicators = new Map();
        this.indicatorCounter = 0;
    }

    create(options = {}) {
        const { x = 0, y = 0, id = null } = options;

        const indicatorId = id || `loading-indicator-${++this.indicatorCounter}`;

        const indicator = document.createElement('div');
        indicator.className = 'link-preview-loading-indicator';
        indicator.id = indicatorId;

        const spinner = document.createElement('div');
        spinner.className = 'link-preview-loading-spinner';
        indicator.appendChild(spinner);

        this.updatePosition(indicator, x, y);
        document.body.appendChild(indicator);

        this.indicators.set(indicatorId, {
            element: indicator,
            x: x,
            y: y,
            createdAt: Date.now()
        });

        console.log(`🔄 创建加载指示器: ${indicatorId} at (${x}, ${y})`);
        return indicatorId;
    }

    updatePosition(indicator, x, y) {
        let element;

        if (typeof indicator === 'string') {
            const indicatorData = this.indicators.get(indicator);
            if (!indicatorData) return;
            element = indicatorData.element;
            indicatorData.x = x;
            indicatorData.y = y;
        } else {
            element = indicator;
        }

        const adjustedX = x + 20;
        const adjustedY = y - 15;

        element.style.left = `${adjustedX}px`;
        element.style.top = `${adjustedY}px`;
    }

    destroy(indicatorId) {
        const indicatorData = this.indicators.get(indicatorId);
        if (!indicatorData) return;

        const { element } = indicatorData;

        element.style.transition = 'opacity 0.2s ease-out';
        element.style.opacity = '0';

        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, 200);

        this.indicators.delete(indicatorId);
        console.log(`🗑️ 销毁加载指示器: ${indicatorId}`);
    }

    createFromMouseEvent(event, id = null) {
        return this.create({
            x: event.clientX,
            y: event.clientY,
            id: id
        });
    }

    destroyAll() {
        const indicatorIds = Array.from(this.indicators.keys());
        indicatorIds.forEach(id => this.destroy(id));
    }
}

// 链接预览管理器
class LinkWindowPreviewManager {
    constructor() {
        this.isEnabled = true;
        this.config = this.getDefaultConfig();
        this.previewWindows = new Map();
        this.windowCounter = 0;
        this.customKey = 'Alt';
        this.triggerMethod = 'alt+click';
        this.triggerDelay = 300;

        // 事件处理器（按需创建，避免预分配未使用的处理器）
        this.eventHandlers = new Map();

        // 触发状态管理
        this.hoverTimer = null;
        this.longPressTimer = null;
        this.dragState = {
            isActive: false,
            startTime: 0,
            startX: 0,
            startY: 0,
            currentLink: null,
            indicatorId: null
        };

        // 自定义快捷键+悬停状态管理
        this.hoveredLink = null;
        this.hoveredEvent = null;
        this.isModifierPressed = false;

        // 加载指示器管理
        this.loadingIndicators = new Map();
        this.loadingIndicatorManager = new LinkWindowLoadingIndicator();

        // 用户手动调整记录
        this.userAdjustments = new Map(); // windowId -> {size: boolean, position: boolean}

        // 记忆配置（用于"上次大小"和"上次位置"）
        this.memoryConfig = {
            lastSize: null,
            lastPosition: null
        };

        // 记忆保存防抖定时器
        this.memorySaveTimeout = null;

        // 性能优化常量 - 统一定义
        this.CONSTANTS = LinkWindowConstants;

        // 嵌套弹窗层级管理
        this.zIndexManager = {
            baseZIndex: LinkWindowConstants.Z_INDEX_BASE,
            currentMaxZIndex: LinkWindowConstants.Z_INDEX_BASE,
            windowZIndexMap: new Map(), // 存储每个窗口的z-index

            // 获取新的z-index
            getNextZIndex() {
                this.currentMaxZIndex += 10;
                return this.currentMaxZIndex;
            },

            // 为窗口分配z-index
            assignZIndex(windowId, parentWindowId = null) {
                let zIndex;
                if (parentWindowId && this.windowZIndexMap.has(parentWindowId)) {
                    // 嵌套窗口，比父窗口高10
                    const parentZIndex = this.windowZIndexMap.get(parentWindowId);
                    zIndex = parentZIndex + 10;
                    this.currentMaxZIndex = Math.max(this.currentMaxZIndex, zIndex);
                } else {
                    // 顶级窗口
                    zIndex = this.getNextZIndex();
                }

                this.windowZIndexMap.set(windowId, zIndex);
                LinkWindowUtils.logInfo('层级管理', `分配z-index ${zIndex} 给窗口 ${windowId}${parentWindowId ? ` (父窗口: ${parentWindowId})` : ''}`);
                return zIndex;
            },

            // 窗口置顶
            bringToFront(windowId) {
                const newZIndex = this.getNextZIndex();
                this.windowZIndexMap.set(windowId, newZIndex);
                return newZIndex;
            },

            // 移除窗口的z-index记录
            removeWindow(windowId) {
                this.windowZIndexMap.delete(windowId);
                LinkWindowUtils.logInfo('层级管理', `移除窗口 ${windowId} 的z-index记录`);
            }
        };

        this.init();
    }

    getDefaultConfig() {
        return {
            trigger: {
                method: 'alt+click',
                customKey: 'Alt',
                delay: 300
            },
            window: {
                size: 'medium',
                position: 'center',
                color: '#667eea',
                background: 'default',
                backgroundOpacity: 0.95,
                sizes: {
                    small: { width: 500, height: 600 },
                    medium: { width: 700, height: 800 },
                    large: { width: 900, height: 800 }
                }
            }
        };
    }

    async init() {
        await this.loadConfig();
        this.bindEvents();
        this.loadStyles();
        this.setupMessageListener();
        this.setupTextDragListener();

        // 检测是否在跨标签页窗口中（缓存结果）
        this.isCrossTabWindow = this.detectCrossTabWindow();
        if (this.isCrossTabWindow) {
            LinkWindowUtils.logInfo('环境检测', '检测到跨标签页窗口环境');
        }

        LinkWindowLogger.success('链接预览管理器初始化完成');
    }

    /**
     * 检测是否在跨标签页窗口中（优化：缓存URL参数解析）
     */
    detectCrossTabWindow() {
        // 缓存URL参数解析结果，避免重复创建URLSearchParams对象
        if (!this._urlParams) {
            this._urlParams = new URLSearchParams(window.location.search);
        }
        return this._urlParams.has('__moment_linkwindow_cross_tab');
    }

    async loadConfig() {
        try {
            if (LinkWindowUtils.isChromeApiAvailable()) {
                const response = await chrome.runtime.sendMessage({ type: 'getSettings' });
                if (response.success && response.settings?.linkPreview) {
                    const settings = response.settings.linkPreview;
                    
                    if (settings.trigger?.customKey) {
                        this.customKey = settings.trigger.customKey;
                    }
                    if (settings.trigger?.method) {
                        this.triggerMethod = settings.trigger.method;
                    }
                    if (settings.trigger?.delay !== undefined) {
                        this.triggerDelay = settings.trigger.delay;
                    }
                    if (settings.window) {
                        this.config.window = { ...this.config.window, ...settings.window };
                    }

                    // 加载记忆配置
                    if (settings.memory) {
                        this.memoryConfig = settings.memory;
                        LinkWindowLogger.memory('记忆配置已加载', this.memoryConfig);
                    }

                    LinkWindowLogger.config('链接预览配置已加载');
                }
            }
        } catch (error) {
            console.warn('⚠️ 加载链接预览配置失败，使用默认配置:', error);
        }
    }

    bindEvents() {
        // 移除旧的事件监听器
        this.removeEventListeners();

        // 根据触发方式绑定相应的事件
        switch (this.triggerMethod) {
            case 'alt+click':
                this.bindClickEvents();
                break;
            case 'alt+hover':
                this.bindCustomKeyHoverEvents();
                break;
            case 'longpress':
                this.bindLongPressEvents();
                break;
            case 'drag':
                this.bindDragEvents();
                break;
            case 'hover':
                this.bindHoverEvents();
                break;
            default:
                this.bindClickEvents();
        }

        LinkWindowLogger.success(`已绑定触发方式: ${this.triggerMethod}`);
    }

    /**
     * 统一的事件处理器绑定方法
     * @param {string} eventType - 事件类型
     * @param {Function} handler - 事件处理器
     * @param {boolean} useCapture - 是否使用捕获阶段
     */
    _bindEventHandler(eventType, handler, useCapture = false) {
        // 移除旧的处理器（如果存在）
        this._removeEventHandler(eventType);

        // 绑定新的处理器
        document.addEventListener(eventType, handler, useCapture);

        // 存储处理器引用以便后续清理
        this.eventHandlers.set(eventType, { handler, useCapture });
    }

    /**
     * 移除特定类型的事件处理器
     * @param {string} eventType - 事件类型
     */
    _removeEventHandler(eventType) {
        const handlerInfo = this.eventHandlers.get(eventType);
        if (handlerInfo) {
            document.removeEventListener(eventType, handlerInfo.handler, handlerInfo.useCapture);
            this.eventHandlers.delete(eventType);
        }
    }

    bindClickEvents() {
        const clickHandler = (e) => {
            // 改进的事件委托机制，支持动态内容和嵌套弹窗
            if (!this.isEnabled) return;

            // 检查是否按下了触发修饰键
            if (!this.isCustomKeyPressed(e)) return;

            // 使用事件委托，查找最近的链接元素
            const link = e.target.closest('a[href]');
            if (!link || !this.isValidLink(link)) return;

            // 检查是否在预览窗口内部
            const previewWindow = e.target.closest('.moment-linkwindow-preview');
            let parentWindowId = null;
            if (previewWindow) {
                parentWindowId = previewWindow.id;
                LinkWindowUtils.logInfo('嵌套弹窗检测', '在预览窗口内检测到链接点击，父窗口ID:', parentWindowId);
            }

            e.preventDefault();
            e.stopPropagation();

            // 根据是否在预览窗口内部或跨标签页窗口中决定创建方式
            if (parentWindowId) {
                this.createNestedPreview(link.href, e, parentWindowId);
            } else {
                // 在跨标签页窗口中或普通页面中创建弹窗
                // createPreview 方法内部会自动判断是否需要创建跨标签页窗口
                this.createPreview(link.href, e);
            }
        };

        this._bindEventHandler('click', clickHandler, true);
    }

    bindHoverEvents() {
        this.mouseOverHandler = (e) => {
            if (!this.isEnabled) return;

            const link = e.target.closest('a[href]');
            if (!link || !this.isValidLink(link)) return;

            this.triggerHoverPreview(link, e, 'hover');
        };

        this.mouseOutHandler = (e) => {
            this.clearHoverState();
        };

        document.addEventListener('mouseover', this.mouseOverHandler, true);
        document.addEventListener('mouseout', this.mouseOutHandler, true);
    }

    bindCustomKeyHoverEvents() {
        this.mouseOverHandler = (e) => {
            if (!this.isEnabled) return;

            const link = e.target.closest('a[href]');
            if (!link || !this.isValidLink(link)) return;

            this.hoveredLink = link;
            this.hoveredEvent = e;

            if (this.isModifierPressed) {
                this.triggerCustomKeyHover();
            }
        };

        this.mouseOutHandler = (e) => {
            this.clearCustomKeyHoverState();
        };

        this.keyDownHandler = (e) => {
            if (this.isTargetModifierKey(e)) {
                this.isModifierPressed = true;
                if (this.hoveredLink && this.hoveredEvent) {
                    this.triggerCustomKeyHover();
                }
            }
        };

        this.keyUpHandler = (e) => {
            if (this.isTargetModifierKey(e)) {
                this.isModifierPressed = false;
                this.clearCustomKeyHoverState();
            }
        };

        document.addEventListener('mouseover', this.mouseOverHandler, true);
        document.addEventListener('mouseout', this.mouseOutHandler, true);
        document.addEventListener('keydown', this.keyDownHandler, true);
        document.addEventListener('keyup', this.keyUpHandler, true);
    }

    bindLongPressEvents() {
        this.mouseDownHandler = (e) => {
            if (!this.isEnabled) return;
            if (e.button !== 0) return; // 只处理左键

            const link = e.target.closest('a[href]');
            if (!link || !this.isValidLink(link)) return;

            this.clearLongPressState();

            const delay = this.triggerDelay;
            let indicatorId = null;

            if (delay > 0) {
                indicatorId = this.loadingIndicatorManager.createFromMouseEvent(e, `longpress-${link.href}`);
                this.loadingIndicators.set('longpress', indicatorId);
            }

            this.longPressTimer = setTimeout(() => {
                if (indicatorId) {
                    this.loadingIndicatorManager.destroy(indicatorId);
                    this.loadingIndicators.delete('longpress');
                }

                e.preventDefault();
                e.stopPropagation();

                this.createPreview(link.href, e);
            }, delay);
        };

        this.mouseUpHandler = (e) => {
            this.clearLongPressState();
        };

        this.mouseMoveHandler = (e) => {
            this.clearLongPressState();
        };

        document.addEventListener('mousedown', this.mouseDownHandler, true);
        document.addEventListener('mouseup', this.mouseUpHandler, true);
        document.addEventListener('mousemove', this.mouseMoveHandler, true);
    }

    bindDragEvents() {
        this.mouseDownHandler = (e) => {
            if (e.button !== 0) return;

            const link = e.target.closest('a[href]');
            if (!link) return;

            this.clearDragState();

            this.dragState.isActive = true;
            this.dragState.startTime = Date.now();
            this.dragState.startX = e.clientX;
            this.dragState.startY = e.clientY;
            this.dragState.currentLink = link;

            const delay = this.triggerDelay;
            if (delay > 0) {
                this.dragState.indicatorId = this.loadingIndicatorManager.createFromMouseEvent(
                    e,
                    `drag-trigger-${link.href}`
                );
                this.loadingIndicators.set('dragTrigger', this.dragState.indicatorId);
            }
        };

        this.mouseMoveHandler = (e) => {
            if (!this.dragState.isActive) return;

            const deltaX = Math.abs(e.clientX - this.dragState.startX);
            const deltaY = Math.abs(e.clientY - this.dragState.startY);
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            if (distance > 10) { // 拖拽阈值
                e.preventDefault();
                e.stopPropagation();

                if (this.dragState.indicatorId) {
                    this.loadingIndicatorManager.destroy(this.dragState.indicatorId);
                    this.loadingIndicators.delete('dragTrigger');
                }

                this.createPreview(this.dragState.currentLink.href, e);
                this.clearDragState();
            }
        };

        this.mouseUpHandler = (e) => {
            this.clearDragState();
        };

        document.addEventListener('mousedown', this.mouseDownHandler, true);
        document.addEventListener('mousemove', this.mouseMoveHandler, true);
        document.addEventListener('mouseup', this.mouseUpHandler, true);
    }

    isCustomKeyPressed(e) {
        if (this.triggerMethod === 'longpress' || this.triggerMethod === 'hover' || this.triggerMethod === 'drag') {
            return true;
        }

        switch (this.customKey) {
            case 'Alt': return e.altKey;
            case 'Ctrl': return e.ctrlKey;
            case 'Shift': return e.shiftKey;
            case 'Meta': return e.metaKey;
            default: return e.altKey;
        }
    }

    isTargetModifierKey(e) {
        switch (this.customKey) {
            case 'Alt': return e.key === 'Alt';
            case 'Ctrl': return e.key === 'Control';
            case 'Shift': return e.key === 'Shift';
            case 'Meta': return e.key === 'Meta';
            default: return e.key === 'Alt';
        }
    }

    get isModifierPressed() {
        return this._isModifierPressed || false;
    }

    set isModifierPressed(value) {
        this._isModifierPressed = value;
    }

    triggerHoverPreview(link, event, triggerType) {
        this.clearHoverState();

        const delay = this.triggerDelay;
        let indicatorId = null;

        if (delay > 0) {
            const indicatorPrefix = triggerType === 'custom-hover' ? 'custom-hover' : 'hover';
            indicatorId = this.loadingIndicatorManager.createFromMouseEvent(event, `${indicatorPrefix}-${link.href}`);
            this.loadingIndicators.set('hover', indicatorId);
        }

        this.hoverTimer = setTimeout(() => {
            if (indicatorId) {
                this.loadingIndicatorManager.destroy(indicatorId);
                this.loadingIndicators.delete('hover');
            }

            this.createPreview(link.href, event);

            if (triggerType === 'custom-hover') {
                this.clearCustomKeyHoverState();
            }
        }, delay);
    }

    triggerCustomKeyHover() {
        if (!this.hoveredLink || !this.hoveredEvent) return;
        if (this.hoverTimer) return;

        this.triggerHoverPreview(this.hoveredLink, this.hoveredEvent, 'custom-hover');
    }

    clearHoverState() {
        if (this.hoverTimer) {
            clearTimeout(this.hoverTimer);
            this.hoverTimer = null;
        }

        const indicatorId = this.loadingIndicators.get('hover');
        if (indicatorId) {
            this.loadingIndicatorManager.destroy(indicatorId);
            this.loadingIndicators.delete('hover');
        }
    }

    clearCustomKeyHoverState() {
        this.clearHoverState();
        this.hoveredLink = null;
        this.hoveredEvent = null;
    }

    clearLongPressState() {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }

        const indicatorId = this.loadingIndicators.get('longpress');
        if (indicatorId) {
            this.loadingIndicatorManager.destroy(indicatorId);
            this.loadingIndicators.delete('longpress');
        }
    }

    clearDragState() {
        if (this.dragState.indicatorId) {
            this.loadingIndicatorManager.destroy(this.dragState.indicatorId);
            this.loadingIndicators.delete('dragTrigger');
        }

        this.dragState = {
            isActive: false,
            startTime: 0,
            startX: 0,
            startY: 0,
            currentLink: null,
            indicatorId: null
        };
    }

    isValidLink(link) {
        const href = link.href;
        return href && !href.startsWith('javascript:') && !href.startsWith('mailto:');
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'settingsUpdated') {
                console.log('🔄 收到设置更新通知，重新初始化链接预览');
                this.handleSettingsUpdate(message.settings);
            }
        });
    }

    async handleSettingsUpdate(newSettings) {
        try {
            // 移除旧的事件监听器
            this.removeEventListeners();

            // 更新配置
            if (newSettings?.linkPreview) {
                const settings = newSettings.linkPreview;

                // 更新触发设置
                const triggerUpdates = [];
                let triggerConfigChanged = false;

                if (settings.trigger?.customKey) {
                    this.customKey = settings.trigger.customKey;
                    triggerUpdates.push(`自定义快捷键: ${this.customKey}`);
                    triggerConfigChanged = true;
                }

                if (settings.trigger?.method) {
                    this.triggerMethod = settings.trigger.method;
                    triggerUpdates.push(`触发方式: ${this.triggerMethod}`);
                    triggerConfigChanged = true;
                }

                if (settings.trigger?.delay !== undefined) {
                    this.triggerDelay = settings.trigger.delay;
                    triggerUpdates.push(`触发延迟: ${this.triggerDelay}`);
                    triggerConfigChanged = true;
                }

                if (triggerUpdates.length > 0) {
                    LinkWindowLogger.config('触发设置已更新', triggerUpdates.join(', '));
                }

                // 如果触发配置发生变化，更新所有iframe中的脚本
                if (triggerConfigChanged) {
                    this.updateIframeConfigs();
                }

                // 更新弹窗设置
                let needsMemoryReload = false;
                const windowUpdates = [];

                if (settings.window) {
                    // 批量更新窗口配置
                    const windowSettings = [
                        { key: 'size', label: '大小' },
                        { key: 'position', label: '位置' },
                        { key: 'color', label: '颜色' },
                        { key: 'backgroundOpacity', label: '背景透明度' },
                        { key: 'background', label: '背景' }
                    ];

                    windowSettings.forEach(({ key, label }) => {
                        if (settings.window[key] !== undefined) {
                            this.config.window[key] = settings.window[key];
                            windowUpdates.push(`${label}: ${settings.window[key]}`);

                            // 检查是否需要重新加载记忆配置
                            if ((key === 'size' && settings.window[key] === 'lastSize') ||
                                (key === 'position' && settings.window[key] === 'lastPosition')) {
                                needsMemoryReload = true;
                            }
                        }
                    });

                    if (windowUpdates.length > 0) {
                        LinkWindowLogger.config('弹窗设置已更新', windowUpdates.join(', '));
                    }
                }

                // 更新其他设置
                const otherUpdates = [];

                if (settings.textActions) {
                    this.config.textActions = { ...this.config.textActions, ...settings.textActions };
                    otherUpdates.push('文本拖拽设置');
                }

                if (settings.memory) {
                    this.memoryConfig = settings.memory;
                    otherUpdates.push(`记忆配置: ${JSON.stringify(settings.memory)}`);
                }

                if (settings.enabled !== undefined) {
                    this.isEnabled = settings.enabled !== false;
                    otherUpdates.push(`链接预览状态: ${this.isEnabled ? '启用' : '禁用'}`);
                }

                if (otherUpdates.length > 0) {
                    LinkWindowLogger.config('其他设置已更新', otherUpdates.join(', '));
                }

                // 只在需要时重新加载完整配置（避免不必要的网络请求）
                if (needsMemoryReload) {
                    await this.reloadConfigFromStorage();
                }
            }

            // 重新绑定事件监听器
            this.bindEvents();

            LinkWindowLogger.success('链接预览设置更新完成');
        } catch (error) {
            console.error('❌ 处理设置更新失败:', error);
        }
    }

    removeEventListeners() {
        // 清除所有定时器和状态
        this.clearHoverState();
        this.clearLongPressState();
        this.clearDragState();
        this.clearCustomKeyHoverState();

        // 统一移除所有事件监听器
        for (const [eventType] of this.eventHandlers) {
            this._removeEventHandler(eventType);
        }

        LinkWindowUtils.logInfo('事件清理', '所有事件监听器已移除');
    }

    setupTextDragListener() {
        document.addEventListener('textDragPreview', (e) => {
            const { url, title, text, action, direction, position } = e.detail;
            console.log('📨 收到文本拖拽预览请求:', { url, title, action, direction });

            this.createPreview(url, {
                clientX: position.x,
                clientY: position.y,
                triggerSource: 'textDrag',
                textAction: {
                    action,
                    text,
                    direction,
                    title
                }
            });
        });
    }

    createPreview(url, triggerEvent = null) {
        // 🪟 统一的跨标签页窗口处理
        if (this.isCrossTabWindow) {
            return this._handleCrossTabWindowPreview(url, triggerEvent, 'main');
        }

        return this._createPreviewInternal(url, triggerEvent, { isNested: false });
    }

    /**
     * 创建嵌套预览窗口
     */
    createNestedPreview(url, triggerEvent, parentWindowId) {
        console.log('🔗 创建嵌套预览窗口:', url, '父窗口ID:', parentWindowId);

        // 🪟 统一的跨标签页窗口处理
        if (this.isCrossTabWindow) {
            return this._handleCrossTabWindowPreview(url, triggerEvent, 'nested');
        }

        return this._createPreviewInternal(url, triggerEvent, {
            isNested: true,
            parentWindowId: parentWindowId
        });
    }

    /**
     * 统一处理跨标签页窗口中的预览创建
     * @private
     */
    _handleCrossTabWindowPreview(url, triggerEvent, type) {
        LinkWindowUtils.logInfo('跨标签页处理', `在跨标签页窗口中创建新的跨标签页窗口 (${type}):`, url);
        return this.createCrossTabPreviewFromCrossTab(url, triggerEvent);
    }

    /**
     * 内部统一的预览窗口创建方法
     * @param {string} url - 要预览的URL
     * @param {Event} triggerEvent - 触发事件
     * @param {Object} options - 创建选项
     * @param {boolean} options.isNested - 是否为嵌套窗口
     * @param {string} options.parentWindowId - 父窗口ID（仅嵌套窗口）
     */
    _createPreviewInternal(url, triggerEvent = null, options = {}) {
        const { isNested = false, parentWindowId = null } = options;

        // 统一的前置检查
        if (!this.isEnabled) return;

        if (this.previewWindows.size >= LinkWindowConstants.MAX_WINDOWS) {
            console.log('⚠️ 已达到最大预览窗口数量限制');
            return;
        }

        // 创建加载指示器（统一处理）
        let loadingIndicatorId = null;
        if (triggerEvent && (triggerEvent.clientX !== undefined && triggerEvent.clientY !== undefined)) {
            const indicatorPrefix = isNested ? 'nested-loading' : 'loading';
            loadingIndicatorId = this.loadingIndicatorManager.create({
                x: triggerEvent.clientX,
                y: triggerEvent.clientY,
                id: `${indicatorPrefix}-${Date.now()}`
            });
            console.log(`🔄 ${isNested ? '嵌套' : '主'}弹窗加载指示器已创建:`, loadingIndicatorId);
        }

        // 生成窗口ID和分配z-index
        const windowId = `preview-${++this.windowCounter}`;
        let zIndex;

        if (isNested && parentWindowId) {
            zIndex = this.zIndexManager.assignZIndex(windowId, parentWindowId);
        } else {
            // 对于跨标签页窗口中的弹窗，确保有足够高的z-index
            zIndex = this.zIndexManager.assignZIndex(windowId);
            if (options.isCrossTabOrigin) {
                // 跨标签页窗口中的弹窗需要更高的z-index以确保显示在页面内容之上
                zIndex += 1000;
                LinkWindowUtils.logInfo('层级管理', `跨标签页窗口弹窗z-index调整为: ${zIndex}`);
            }
        }

        // 准备窗口选项
        const windowOptions = {
            zIndex: zIndex,
            parentWindowId: parentWindowId,
            isNested: isNested,
            loadingIndicatorId: loadingIndicatorId
        };

        // 准备触发事件（统一处理）
        const enhancedTriggerEvent = triggerEvent ? {
            ...triggerEvent,
            nestedOptions: isNested ? windowOptions : undefined,
            loadingIndicatorId: loadingIndicatorId
        } : {
            nestedOptions: isNested ? windowOptions : undefined,
            loadingIndicatorId: loadingIndicatorId
        };

        console.log(`🔗 创建${isNested ? '嵌套' : '主'}链接预览: ${url}`);

        // 创建和配置窗口
        const window = this.createPreviewWindow(windowId, url, enhancedTriggerEvent);
        this.previewWindows.set(windowId, window);
        document.body.appendChild(window);

        this.applyWindowStyles(window, enhancedTriggerEvent);
        this.bindWindowEvents(window);

        // 弹窗创建完成后立即清理触发加载指示器
        // 这是正确的时机：弹窗已出现，触发过程结束
        if (loadingIndicatorId) {
            this.loadingIndicatorManager.destroy(loadingIndicatorId);
            LinkWindowUtils.logInfo('触发完成', `${isNested ? '嵌套' : '主'}弹窗触发加载指示器已清理:`, loadingIndicatorId);
        }

        return window;
    }

    createPreviewWindow(windowId, url, triggerEvent = null) {
        const window = document.createElement('div');
        window.id = windowId;
        window.className = 'moment-linkwindow-preview';

        let displayTitle;
        if (triggerEvent && triggerEvent.triggerSource === 'textDrag' && triggerEvent.textAction) {
            displayTitle = triggerEvent.textAction.title;
            // 🎯 修复：移除特殊CSS类，确保与链接弹窗样式一致
            // window.classList.add('text-drag-preview'); // 已移除
        } else {
            const domain = LinkWindowUtils.extractDomain(url);
            displayTitle = domain;
        }

        window.innerHTML = `
            <div class="preview-header">
                <div class="preview-url-bar" title="${url}">${LinkWindowUtils.formatUrlForDisplay(url)}</div>
                <div class="preview-title">${displayTitle}</div>
                <div class="preview-controls">
                    <button class="preview-pin" title="升级为跨标签页窗口">📌</button>
                    <button class="preview-minimize" title="最小化">➖</button>
                    <button class="preview-close" title="关闭">✕</button>
                </div>
            </div>
            <div class="preview-content">
                <iframe src="${url}" frameborder="0" sandbox="allow-scripts allow-same-origin allow-forms allow-popups"></iframe>
            </div>
            <div class="preview-resize-handle" title="拖拽调整大小">⋮⋮</div>
        `;

        return window;
    }

    applyWindowStyles(previewWindow, triggerEvent = null) {
        const windowId = previewWindow.id;
        const userAdjustment = this.userAdjustments.get(windowId) || { size: false, position: false };

        // 应用窗口大小（只有在用户没有手动调整过时才应用）
        if (!userAdjustment.size) {
            const { width, height } = this.getWindowSize();
            Object.assign(previewWindow.style, {
                width: `${width}px`,
                height: `${height}px`
            });
        }

        // 应用窗口位置（只有在用户没有手动调整过时才应用）
        if (!userAdjustment.position) {
            const { width, height } = this.getWindowSize();
            const { left, top } = this.getWindowPosition(width, height, triggerEvent);
            Object.assign(previewWindow.style, {
                left: `${left}px`,
                top: `${top}px`
            });
        }

        // 应用z-index - 支持嵌套窗口层级管理
        let zIndex;
        if (triggerEvent && triggerEvent.nestedOptions && triggerEvent.nestedOptions.zIndex) {
            // 嵌套窗口使用预分配的z-index
            zIndex = triggerEvent.nestedOptions.zIndex;
        } else {
            // 普通窗口使用z-index管理器分配
            zIndex = this.zIndexManager.assignZIndex(previewWindow.id);
        }
        previewWindow.style.zIndex = zIndex;

        this.applyWindowTheme(previewWindow);
    }

    getWindowSize() {
        const size = this.config.window.size;

        // 如果是"上次大小"且有记忆的大小，使用记忆的大小
        if (size === 'lastSize' && this.memoryConfig?.lastSize) {
            console.log('📏 使用记忆的窗口大小:', this.memoryConfig.lastSize);
            return this.memoryConfig.lastSize;
        }

        // 使用预设大小
        const sizes = this.config.window.sizes;
        return sizes[size] || sizes.medium;
    }

    getWindowPosition(width, height, triggerEvent) {
        const position = this.config.window.position;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 如果是"上次位置"且有记忆的位置，使用记忆的位置
        if (position === 'lastPosition' && this.memoryConfig?.lastPosition) {
            const lastPos = this.memoryConfig.lastPosition;
            console.log('📍 使用记忆的窗口位置:', lastPos);
            // 确保位置在视口内
            const left = Math.max(10, Math.min(lastPos.left, viewportWidth - width - 10));
            const top = Math.max(10, Math.min(lastPos.top, viewportHeight - height - 10));
            return { left, top };
        }

        let left, top;

        if (position === 'mouse' && triggerEvent) {
            left = triggerEvent.clientX - width / 2;
            top = triggerEvent.clientY - height / 2;
        } else if (position === 'left') {
            left = 50;
            top = (viewportHeight - height) / 2;
        } else if (position === 'right') {
            left = viewportWidth - width - 50;
            top = (viewportHeight - height) / 2;
        } else { // center
            left = (viewportWidth - width) / 2;
            top = (viewportHeight - height) / 2;
        }

        // 确保窗口在视口内
        left = Math.max(10, Math.min(left, viewportWidth - width - 10));
        top = Math.max(10, Math.min(top, viewportHeight - height - 10));

        return { left, top };
    }

    applyWindowTheme(previewWindow) {
        const color = this.config.window.color;
        const header = previewWindow.querySelector('.preview-header');
        if (header) {
            header.style.background = `linear-gradient(135deg, ${color}, ${color}dd)`;
        }
    }

    // 保存窗口大小到记忆（优化：延迟保存避免频繁操作）
    saveWindowSize(width, height) {
        if (this.memoryConfig) {
            this.memoryConfig.lastSize = { width, height };
            console.log('💾 窗口大小已保存到记忆:', { width, height });
            this.debouncedSaveMemoryConfig();
        }
    }

    // 保存窗口位置到记忆（优化：延迟保存避免频繁操作）
    saveWindowPosition(left, top) {
        if (this.memoryConfig) {
            this.memoryConfig.lastPosition = { left, top };
            console.log('💾 窗口位置已保存到记忆:', { left, top });
            this.debouncedSaveMemoryConfig();
        }
    }

    // 防抖保存记忆配置（优化：避免频繁存储操作）
    debouncedSaveMemoryConfig() {
        if (this.memorySaveTimeout) {
            clearTimeout(this.memorySaveTimeout);
        }
        this.memorySaveTimeout = setTimeout(() => {
            this.saveMemoryConfig();
            this.memorySaveTimeout = null;
        }, 500); // 500ms 防抖延迟
    }

    // 保存记忆配置到存储
    async saveMemoryConfig() {
        try {
            if (LinkWindowUtils.isChromeApiAvailable()) {
                const response = await chrome.runtime.sendMessage({
                    type: 'updateMemoryConfig',
                    memory: this.memoryConfig
                });

                if (response.success) {
                    LinkWindowLogger.success('记忆配置保存成功');
                } else {
                    LinkWindowLogger.error('记忆配置保存失败', response.error);
                }
            }
        } catch (error) {
            console.error('❌ 保存记忆配置异常:', error);
        }
    }

    // 重新从存储中加载完整配置（优化：避免重复代码）
    async reloadConfigFromStorage() {
        try {
            if (LinkWindowUtils.isChromeApiAvailable()) {
                const response = await chrome.runtime.sendMessage({ type: 'getSettings' });
                if (response.success && response.settings?.linkPreview) {
                    const settings = response.settings.linkPreview;

                    // 更新记忆配置
                    if (settings.memory) {
                        this.memoryConfig = settings.memory;
                        console.log('🧠 记忆配置已重新加载:', this.memoryConfig);
                    } else {
                        console.log('📝 存储中没有记忆配置，使用默认值');
                    }

                    // 同时更新其他可能变化的配置
                    if (settings.window) {
                        this.config.window = { ...this.config.window, ...settings.window };
                    }
                } else {
                    console.log('📝 存储中没有配置，使用默认值');
                }
            }
        } catch (error) {
            console.error('❌ 重新加载配置异常:', error);
        }
    }

    bindWindowEvents(previewWindow) {
        const closeBtn = previewWindow.querySelector('.preview-close');
        const pinBtn = previewWindow.querySelector('.preview-pin');
        const minimizeBtn = previewWindow.querySelector('.preview-minimize');
        const header = previewWindow.querySelector('.preview-header');
        const iframe = previewWindow.querySelector('iframe');

        closeBtn.addEventListener('click', () => {
            this.closePreview(previewWindow.id);
        });

        pinBtn.addEventListener('click', () => {
            this.createCrossTabPreview(previewWindow);
        });

        minimizeBtn.addEventListener('click', () => {
            this.toggleMinimize(previewWindow);
        });

        // iframe加载完成后的处理
        const loadHandler = () => {
            // 重新应用主题色，确保在iframe加载完成后主题色仍然生效
            this.applyWindowTheme(previewWindow);

            // 为iframe内部绑定嵌套弹窗事件处理
            this.bindIframeEvents(iframe, previewWindow.id);

            LinkWindowUtils.logSuccess('iframe处理', 'iframe加载完成并已绑定事件');
        };

        iframe.addEventListener('load', loadHandler);

        // iframe加载错误处理
        iframe.addEventListener('error', () => {
            // 在iframe内容区域显示错误信息
            const contentDiv = previewWindow.querySelector('.preview-content');
            if (contentDiv) {
                contentDiv.innerHTML = `
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: #dc3545; font-size: 16px;">
                        <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                        <div>页面加载失败</div>
                    </div>
                `;
            }
            LinkWindowUtils.logError('iframe加载', 'iframe加载失败');
        });

        // 窗口点击置顶功能
        const bringToFrontHandler = () => {
            const windowId = previewWindow.id;
            const newZIndex = this.zIndexManager.bringToFront(windowId);
            previewWindow.style.zIndex = newZIndex;
            LinkWindowUtils.logInfo('窗口置顶', `窗口 ${windowId} 已置顶，新z-index: ${newZIndex}`);
        };
        previewWindow.addEventListener('mousedown', bringToFrontHandler);

        // 绑定拖拽和调整大小功能
        const dragCleanup = this.makeDraggable(previewWindow, header);
        const resizeCleanup = this.makeResizable(previewWindow);

        // 存储清理函数
        previewWindow._eventCleanup = () => {
            if (dragCleanup) dragCleanup();
            if (resizeCleanup) resizeCleanup();
            previewWindow.removeEventListener('mousedown', bringToFrontHandler);

            // 清理iframe的嵌套事件处理
            if (iframe && iframe._nestedEventCleanup) {
                iframe._nestedEventCleanup.forEach(cleanup => cleanup());
                iframe._nestedEventCleanup = [];
            }
        };
    }

    /**
     * 为iframe内部绑定嵌套弹窗事件处理（修复：支持所有触发方式）
     */
    bindIframeEvents(iframe, parentWindowId) {
        try {
            // 检查是否可以访问iframe内容（同源策略）
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (!iframeDoc) {
                console.log('⚠️ 无法访问iframe内容，可能是跨域限制');
                return;
            }

            console.log('🔗 为iframe绑定嵌套弹窗事件处理，父窗口ID:', parentWindowId, '触发方式:', this.triggerMethod);

            // 🎯 修复：根据用户配置的触发方式绑定相应的事件
            this.bindIframeEventsByTriggerMethod(iframeDoc, parentWindowId, iframe);

            console.log('✅ iframe嵌套事件处理已绑定，触发方式:', this.triggerMethod);

        } catch (error) {
            // 跨域或其他访问限制
            console.log('⚠️ 无法为iframe绑定事件，可能是跨域限制:', error.message);

            // 对于跨域iframe，使用消息传递机制
            this.setupCrossOriginIframeHandler(iframe, parentWindowId);
        }
    }

    /**
     * 根据触发方式为iframe绑定相应的事件
     * @private
     */
    bindIframeEventsByTriggerMethod(iframeDoc, parentWindowId, iframe) {
        // 根据触发方式绑定相应的事件（与主页面逻辑保持一致）
        switch (this.triggerMethod) {
            case 'alt+click':
                this.bindIframeClickEvents(iframeDoc, parentWindowId, iframe);
                break;
            case 'alt+hover':
                this.bindIframeCustomKeyHoverEvents(iframeDoc, parentWindowId, iframe);
                break;
            case 'longpress':
                this.bindIframeLongPressEvents(iframeDoc, parentWindowId, iframe);
                break;
            case 'drag':
                this.bindIframeDragEvents(iframeDoc, parentWindowId, iframe);
                break;
            case 'hover':
                this.bindIframeHoverEvents(iframeDoc, parentWindowId, iframe);
                break;
            default:
                this.bindIframeClickEvents(iframeDoc, parentWindowId, iframe);
        }
    }

    /**
     * 创建通用的iframe事件处理器（优化：消除重复代码）
     * @private
     */
    createIframeEventHandler(parentWindowId, actionName, keyCheck = true) {
        return (e) => {
            // 统一的键检查逻辑
            if (keyCheck && !this.isCustomKeyPressed(e)) return;

            // 统一的链接查找和验证逻辑
            const link = e.target.closest('a[href]');
            if (!link || !this.isValidLink(link)) return;

            // 阻止默认行为（仅对点击事件）
            if (e.type === 'click') {
                e.preventDefault();
                e.stopPropagation();
            }

            console.log(`🔗 iframe内检测到链接${actionName}，创建嵌套预览:`, link.href);
            this.createNestedPreview(link.href, e, parentWindowId);
        };
    }

    /**
     * 为iframe绑定点击事件（Alt+点击触发）
     * @private
     */
    bindIframeClickEvents(iframeDoc, parentWindowId, iframe) {
        const handler = this.createIframeEventHandler(parentWindowId, '点击');
        iframeDoc.addEventListener('click', handler, true);
        this.addIframeEventCleanup(iframe, () => {
            iframeDoc.removeEventListener('click', handler, true);
        });
    }

    /**
     * 为iframe绑定悬停事件（Alt+悬停触发）
     * @private
     */
    bindIframeCustomKeyHoverEvents(iframeDoc, parentWindowId, iframe) {
        const handler = this.createIframeEventHandler(parentWindowId, '悬停');
        iframeDoc.addEventListener('mouseover', handler, true);
        this.addIframeEventCleanup(iframe, () => {
            iframeDoc.removeEventListener('mouseover', handler, true);
        });
    }

    /**
     * 为iframe绑定长按事件（优化：简化逻辑）
     * @private
     */
    bindIframeLongPressEvents(iframeDoc, parentWindowId, iframe) {
        let longPressTimer = null;
        let longPressTarget = null;

        const mouseDownHandler = (e) => {
            const link = e.target.closest('a[href]');
            if (!link || !this.isValidLink(link)) return;

            longPressTarget = link;
            longPressTimer = setTimeout(() => {
                if (longPressTarget === link) {
                    console.log('🔗 iframe内检测到链接长按，创建嵌套预览:', link.href);
                    this.createNestedPreview(link.href, e, parentWindowId);
                }
            }, this.triggerDelay);
        };

        const clearLongPress = () => {
            if (longPressTimer) {
                clearTimeout(longPressTimer);
                longPressTimer = null;
            }
            longPressTarget = null;
        };

        // 绑定事件
        iframeDoc.addEventListener('mousedown', mouseDownHandler, true);
        iframeDoc.addEventListener('mouseup', clearLongPress, true);
        iframeDoc.addEventListener('mouseleave', clearLongPress, true);

        // 存储清理函数
        this.addIframeEventCleanup(iframe, () => {
            clearLongPress();
            iframeDoc.removeEventListener('mousedown', mouseDownHandler, true);
            iframeDoc.removeEventListener('mouseup', clearLongPress, true);
            iframeDoc.removeEventListener('mouseleave', clearLongPress, true);
        });
    }

    /**
     * 为iframe绑定纯悬停事件（优化：使用通用处理器）
     * @private
     */
    bindIframeHoverEvents(iframeDoc, parentWindowId, iframe) {
        const handler = this.createIframeEventHandler(parentWindowId, '悬停', false);
        iframeDoc.addEventListener('mouseover', handler, true);
        this.addIframeEventCleanup(iframe, () => {
            iframeDoc.removeEventListener('mouseover', handler, true);
        });
    }

    /**
     * 为iframe绑定拖拽事件（优化：简化实现）
     * @private
     */
    bindIframeDragEvents(iframeDoc, parentWindowId, iframe) {
        let dragState = null;

        const mouseDownHandler = (e) => {
            const link = e.target.closest('a[href]');
            if (!link || !this.isValidLink(link)) return;

            dragState = {
                link,
                startX: e.clientX,
                startY: e.clientY,
                triggered: false
            };
        };

        const mouseMoveHandler = (e) => {
            if (!dragState || dragState.triggered) return;

            const distance = Math.sqrt(
                Math.pow(e.clientX - dragState.startX, 2) +
                Math.pow(e.clientY - dragState.startY, 2)
            );

            if (distance > 10) {
                dragState.triggered = true;
                console.log('🔗 iframe内检测到链接拖拽，创建嵌套预览:', dragState.link.href);
                this.createNestedPreview(dragState.link.href, e, parentWindowId);
            }
        };

        const mouseUpHandler = () => {
            dragState = null;
        };

        // 绑定事件
        iframeDoc.addEventListener('mousedown', mouseDownHandler, true);
        iframeDoc.addEventListener('mousemove', mouseMoveHandler, true);
        iframeDoc.addEventListener('mouseup', mouseUpHandler, true);

        // 存储清理函数
        this.addIframeEventCleanup(iframe, () => {
            iframeDoc.removeEventListener('mousedown', mouseDownHandler, true);
            iframeDoc.removeEventListener('mousemove', mouseMoveHandler, true);
            iframeDoc.removeEventListener('mouseup', mouseUpHandler, true);
        });
    }

    /**
     * 添加iframe事件清理函数
     * @private
     */
    addIframeEventCleanup(iframe, cleanupFn) {
        if (!iframe._nestedEventCleanup) {
            iframe._nestedEventCleanup = [];
        }
        iframe._nestedEventCleanup.push(() => {
            try {
                cleanupFn();
            } catch (error) {
                // 忽略清理时的错误
            }
        });
    }

    /**
     * 设置跨域iframe的消息处理机制
     */
    setupCrossOriginIframeHandler(iframe, parentWindowId) {
        console.log('🌐 设置跨域iframe消息处理机制');

        // 监听来自iframe的消息
        const messageHandler = (event) => {
            // 检查消息来源
            if (event.source !== iframe.contentWindow) return;

            // 检查消息类型
            if (event.data && event.data.type === 'momentLinkWindowLinkClick') {
                const { url, triggerPosition } = event.data;
                console.log('📨 收到iframe链接点击消息:', url);

                // 创建模拟的触发事件，包含位置信息以支持加载指示器
                const simulatedTriggerEvent = triggerPosition ? {
                    clientX: triggerPosition.x,
                    clientY: triggerPosition.y,
                    type: 'nested-trigger',
                    timestamp: Date.now()
                } : null;

                // 创建嵌套预览窗口
                this.createNestedPreview(url, simulatedTriggerEvent, parentWindowId);
            }
        };

        window.addEventListener('message', messageHandler);

        // 存储清理函数
        if (!iframe._nestedEventCleanup) {
            iframe._nestedEventCleanup = [];
        }
        iframe._nestedEventCleanup.push(() => {
            window.removeEventListener('message', messageHandler);
        });

        // 使用 background script 注入脚本到所有 frame
        this.injectIframeScriptViaBackground(iframe, parentWindowId);
    }

    /**
     * 通过 background script 注入 iframe 脚本
     */
    injectIframeScriptViaBackground(iframe, parentWindowId) {
        try {
            // 检查是否在扩展环境中
            if (!LinkWindowUtils.isExtensionEnvironment()) {
                LinkWindowUtils.logWarning('脚本注入', '非扩展环境，无法使用 background script 注入');
                return;
            }

            // 获取iframe的URL
            const iframeUrl = iframe.src;
            if (!iframeUrl) {
                LinkWindowUtils.logWarning('脚本注入', 'iframe没有src属性，无法注入脚本');
                return;
            }

            // 获取当前的触发配置
            const triggerConfig = this.getTriggerConfig();

            LinkWindowUtils.logInfo('脚本注入', '请求 background script 注入iframe脚本:', iframeUrl);

            // 向background script发送消息，请求注入脚本
            chrome.runtime.sendMessage({
                type: 'injectIframeScript',
                data: {
                    url: iframeUrl,
                    parentWindowId: parentWindowId,
                    triggerConfig: triggerConfig,
                    delay: this.triggerDelay || LinkWindowConstants.DEFAULT_DELAY
                }
            }, (response) => {
                if (response && response.success) {
                    LinkWindowUtils.logSuccess('脚本注入', 'iframe脚本注入成功');
                } else {
                    LinkWindowUtils.logError('脚本注入', 'iframe脚本注入失败:', response?.error || 'Unknown error');
                }
            });

        } catch (error) {
            LinkWindowUtils.logError('脚本注入', 'background script 注入失败:', error.message);
        }
    }

    /**
     * 获取当前的触发配置
     */
    getTriggerConfig() {
        // 构建修饰键检查表达式
        let checkExpression = 'false';
        switch (this.customKey) {
            case 'Alt':
                checkExpression = 'event.altKey && !event.ctrlKey && !event.shiftKey && !event.metaKey';
                break;
            case 'Ctrl':
                checkExpression = 'event.ctrlKey && !event.altKey && !event.shiftKey && !event.metaKey';
                break;
            case 'Shift':
                checkExpression = 'event.shiftKey && !event.altKey && !event.ctrlKey && !event.metaKey';
                break;
            case 'Meta':
                checkExpression = 'event.metaKey && !event.altKey && !event.ctrlKey && !event.shiftKey';
                break;
            default:
                checkExpression = 'event.altKey && !event.ctrlKey && !event.shiftKey && !event.metaKey';
        }

        return {
            method: this.triggerMethod || 'alt+click',
            key: this.customKey || 'Alt',
            checkExpression: checkExpression
        };
    }

    /**
     * 更新所有iframe中的触发配置
     */
    updateIframeConfigs() {
        console.log('🔄 开始更新所有iframe的触发配置');

        // 获取当前页面中所有的预览窗口
        const previewWindows = document.querySelectorAll('.moment-linkwindow-preview');
        let hasActiveIframes = false;

        // 获取一次触发配置，避免重复调用
        const newTriggerConfig = this.getTriggerConfig();
        const delay = this.triggerDelay || 300;

        // 首先尝试通过postMessage更新现有iframe
        previewWindows.forEach(previewWindow => {
            const iframe = previewWindow.querySelector('iframe');
            if (iframe && iframe.contentWindow) {
                try {
                    // 向iframe发送配置更新消息
                    iframe.contentWindow.postMessage({
                        type: 'momentLinkWindowConfigUpdate',
                        triggerConfig: newTriggerConfig,
                        delay: delay,
                        timestamp: Date.now()
                    }, '*');

                    hasActiveIframes = true;
                    console.log('📨 已向iframe发送配置更新消息:', previewWindow.id);
                } catch (error) {
                    console.log('⚠️ 无法向iframe发送配置更新消息:', error.message);
                }
            }
        });

        // 只有在有活跃iframe时才进行background script重新注入
        // 传递已获取的配置，避免重复调用
        if (hasActiveIframes) {
            this.reinjectIframeScripts(newTriggerConfig, delay);
        } else {
            console.log('📝 没有活跃的iframe，跳过重新注入');
        }
    }

    /**
     * 重新注入iframe脚本到所有frame
     * @param {Object} triggerConfig - 可选的预获取触发配置，避免重复调用
     * @param {number} delay - 可选的预获取延迟值
     */
    reinjectIframeScripts(triggerConfig = null, delay = null) {
        try {
            if (!LinkWindowUtils.isExtensionEnvironment()) {
                LinkWindowUtils.logWarning('脚本重注入', '非扩展环境，无法重新注入iframe脚本');
                return;
            }

            // 使用传入的配置或获取新配置
            const config = triggerConfig || this.getTriggerConfig();
            const delayValue = delay !== null ? delay : (this.triggerDelay || LinkWindowConstants.DEFAULT_DELAY);

            LinkWindowUtils.logInfo('脚本重注入', '请求background script重新注入iframe脚本');

            // 向background script发送重新注入请求
            chrome.runtime.sendMessage({
                type: 'reinjectAllIframeScripts',
                data: {
                    triggerConfig: config,
                    delay: delayValue
                }
            }, (response) => {
                if (response && response.success) {
                    LinkWindowUtils.logSuccess('脚本重注入', 'iframe脚本重新注入成功');
                } else {
                    LinkWindowUtils.logError('脚本重注入', 'iframe脚本重新注入失败:', response?.error || 'Unknown error');
                }
            });

        } catch (error) {
            LinkWindowUtils.logError('脚本重注入', '重新注入iframe脚本失败:', error.message);
        }
    }

    async createCrossTabPreview(previewWindow) {
        try {
            const iframe = previewWindow.querySelector('iframe');
            const url = iframe.src;
            const rect = previewWindow.getBoundingClientRect();

            const response = await chrome.runtime.sendMessage({
                type: 'createCrossTabPreview',
                data: {
                    url,
                    options: {
                        width: Math.round(rect.width),
                        height: Math.round(rect.height),
                        left: Math.round(rect.left),
                        top: Math.round(rect.top)
                    }
                }
            });

            if (response.success) {
                this.closePreview(previewWindow.id);
                LinkWindowLogger.window('成功升级为跨标签页窗口', response.windowId);
            }
        } catch (error) {
            console.error('❌ 升级为跨标签页窗口失败:', error);
        }
    }

    /**
     * 在跨标签页窗口中创建新的跨标签页窗口
     * 这确保了真正的独立性，不受父窗口限制
     */
    async createCrossTabPreviewFromCrossTab(url, triggerEvent = null) {
        try {
            if (!LinkWindowUtils.isExtensionEnvironment()) {
                LinkWindowUtils.logWarning('跨标签页创建', '非扩展环境，无法创建跨标签页窗口');
                return;
            }

            // 计算新窗口位置和尺寸，继承当前窗口尺寸
            const options = this.calculateCrossTabWindowPosition(triggerEvent);

            LinkWindowUtils.logInfo('跨标签页创建', '从跨标签页窗口创建新窗口:', url);

            const response = await chrome.runtime.sendMessage({
                type: 'createCrossTabPreview',
                data: {
                    url,
                    options: options
                }
            });

            if (response.success) {
                LinkWindowUtils.logSuccess('跨标签页创建', '成功创建新的跨标签页窗口:', response.windowId);

                // 清理触发加载指示器（如果有）
                if (triggerEvent && triggerEvent.loadingIndicatorId) {
                    this.loadingIndicatorManager.destroy(triggerEvent.loadingIndicatorId);
                    LinkWindowUtils.logInfo('触发完成', '跨标签页窗口触发加载指示器已清理:', triggerEvent.loadingIndicatorId);
                }
            } else {
                LinkWindowUtils.logError('跨标签页创建', '创建跨标签页窗口失败:', response.error);
            }

            return response;
        } catch (error) {
            LinkWindowUtils.logError('跨标签页创建', '创建跨标签页窗口异常:', error.message);
        }
    }

    /**
     * 计算跨标签页窗口的尺寸和位置配置
     * 优化：合并尺寸继承和位置计算逻辑，减少重复计算
     */
    calculateCrossTabWindowPosition(triggerEvent = null) {
        // 获取当前窗口尺寸（继承给新窗口）
        const windowSize = this._getCurrentWindowSize();

        // 计算新窗口位置
        const position = this._calculateNewWindowPosition(triggerEvent, windowSize);

        const windowOptions = {
            width: windowSize.width,
            height: windowSize.height,
            left: position.left,
            top: position.top
        };

        LinkWindowUtils.logInfo('窗口配置', `新窗口配置: 尺寸=${windowOptions.width}x${windowOptions.height}, 位置=(${windowOptions.left}, ${windowOptions.top})`);

        return windowOptions;
    }

    /**
     * 获取当前窗口尺寸
     * @private
     */
    _getCurrentWindowSize() {
        const width = window.outerWidth || 800;
        const height = window.outerHeight || 600;

        LinkWindowUtils.logInfo('尺寸继承', `继承当前窗口尺寸: ${width}x${height}`);

        return { width, height };
    }

    /**
     * 计算新窗口位置，确保不超出屏幕边界
     * @private
     */
    _calculateNewWindowPosition(triggerEvent, windowSize) {
        const { width, height } = windowSize;
        const margin = 50;

        // 计算屏幕边界限制
        const maxLeft = screen.width - width - margin;
        const maxTop = screen.height - height - margin;

        let left, top;

        if (triggerEvent && triggerEvent.clientX !== undefined && triggerEvent.clientY !== undefined) {
            // 基于点击位置计算
            const screenX = window.screenX + triggerEvent.clientX;
            const screenY = window.screenY + triggerEvent.clientY;

            left = Math.min(Math.max(screenX - 100, margin), maxLeft);
            top = Math.min(Math.max(screenY - 50, margin), maxTop);

            LinkWindowUtils.logInfo('位置计算', `基于点击位置计算: (${left}, ${top})`);
        } else {
            // 基于当前窗口位置偏移
            left = Math.min(window.screenX + margin, maxLeft);
            top = Math.min(window.screenY + margin, maxTop);

            LinkWindowUtils.logInfo('位置计算', `基于窗口偏移计算: (${left}, ${top})`);
        }

        return { left, top };
    }

    toggleMinimize(previewWindow) {
        const content = previewWindow.querySelector('.preview-content');
        const isMinimized = previewWindow.classList.contains('minimized');

        if (isMinimized) {
            previewWindow.classList.remove('minimized');
            content.style.display = 'block';
        } else {
            previewWindow.classList.add('minimized');
            content.style.display = 'none';
        }
    }

    makeDraggable(previewWindow, handle) {
        let isDragging = false;
        let startX, startY, startLeft, startTop;
        let animationFrameId = null;
        let pendingUpdate = false;
        let currentMouseX, currentMouseY;
        let globalEventsAttached = false;

        // 清理拖拽状态
        const cleanupDragState = () => {
            // 重置状态变量
            isDragging = false;
            pendingUpdate = false;

            // 清理动画帧
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }

            // 移除样式
            previewWindow.classList.remove('dragging');
            document.body.style.cursor = '';
            document.body.style.userSelect = '';

            // 移除全局事件监听器
            if (globalEventsAttached) {
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
                globalEventsAttached = false;
            }
        };

        const onMouseDown = (e) => {
            if (e.target.closest('.preview-controls')) return;

            // 清理任何残留状态
            cleanupDragState();

            // 初始化拖拽状态
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt(previewWindow.style.left) || 0;
            startTop = parseInt(previewWindow.style.top) || 0;
            currentMouseX = e.clientX;
            currentMouseY = e.clientY;

            // 添加拖拽样式
            previewWindow.classList.add('dragging');
            document.body.style.cursor = 'move';
            document.body.style.userSelect = 'none';

            // 绑定全局事件监听器
            document.addEventListener('mousemove', onMouseMove, { passive: false });
            document.addEventListener('mouseup', onMouseUp, { passive: false });
            globalEventsAttached = true;

            console.log('🎯 开始拖拽窗口:', previewWindow.id);

            e.preventDefault();
            e.stopPropagation();
        };

        const updatePosition = () => {
            if (!isDragging || !pendingUpdate) return;

            const deltaX = currentMouseX - startX;
            const deltaY = currentMouseY - startY;

            const left = startLeft + deltaX;
            const top = startTop + deltaY;

            // 限制在视口内
            const maxLeft = window.innerWidth - previewWindow.offsetWidth;
            const maxTop = window.innerHeight - previewWindow.offsetHeight;

            const finalLeft = Math.max(0, Math.min(left, maxLeft));
            const finalTop = Math.max(0, Math.min(top, maxTop));

            previewWindow.style.left = `${finalLeft}px`;
            previewWindow.style.top = `${finalTop}px`;

            pendingUpdate = false;
            animationFrameId = null;
        };

        const onMouseMove = (e) => {
            if (!isDragging) return;

            currentMouseX = e.clientX;
            currentMouseY = e.clientY;

            // 使用 requestAnimationFrame 优化渲染性能
            if (!pendingUpdate) {
                pendingUpdate = true;
                animationFrameId = requestAnimationFrame(updatePosition);
            }

            e.preventDefault();
        };

        const onMouseUp = (e) => {
            if (!isDragging) return;

            // 标记为用户手动调整位置
            const windowId = previewWindow.id;
            const userAdjustment = this.userAdjustments.get(windowId) || { size: false, position: false };
            userAdjustment.position = true;
            this.userAdjustments.set(windowId, userAdjustment);

            // 保存位置到记忆
            const left = parseInt(previewWindow.style.left);
            const top = parseInt(previewWindow.style.top);
            this.saveWindowPosition(left, top);

            LinkWindowLogger.drag(`拖拽窗口完成: ${previewWindow.id}`, `位置: (${left}, ${top})`);
            cleanupDragState();
            e.preventDefault();
        };

        handle.addEventListener('mousedown', onMouseDown, { passive: false });

        // 返回清理函数
        return () => {
            handle.removeEventListener('mousedown', onMouseDown);
            cleanupDragState();
        };
    }

    /**
     * 共享的鼠标范围限制方法
     */
    limitMouseRange(mouseX, mouseY, handle, direction, maxDistance = this.PERFORMANCE_CONSTANTS.MOUSE_RANGE_LIMIT) {
        const handleRect = handle.getBoundingClientRect();
        const distanceX = Math.abs(mouseX - (handleRect.left + handleRect.width / 2));
        const distanceY = Math.abs(mouseY - (handleRect.top + handleRect.height / 2));

        if (distanceX > maxDistance || distanceY > maxDistance) {
            const elementRect = handle.closest('.moment-linkwindow-preview').getBoundingClientRect();
            if (direction === 'se') {
                return {
                    x: Math.min(mouseX, elementRect.right + maxDistance),
                    y: Math.min(mouseY, elementRect.bottom + maxDistance)
                };
            }
        }
        return { x: mouseX, y: mouseY };
    }

    makeResizable(previewWindow) {
        const resizeHandle = previewWindow.querySelector('.preview-resize-handle');
        if (!resizeHandle) {
            console.warn('⚠️ 未找到调整大小手柄');
            return () => {};
        }

        let isResizing = false;
        let startX, startY, startWidth, startHeight;
        let animationFrameId = null;
        let pendingUpdate = false;
        let currentMouseX, currentMouseY;
        let globalEventsAttached = false;
        let saveTimeout = null;

        // 清理调整大小状态
        const cleanupResizeState = () => {
            // 重置状态变量
            isResizing = false;
            pendingUpdate = false;

            // 清理动画帧
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }

            // 清理保存定时器
            if (saveTimeout) {
                clearTimeout(saveTimeout);
                saveTimeout = null;
            }

            // 移除样式
            previewWindow.classList.remove('resizing');
            document.body.style.cursor = '';
            document.body.style.userSelect = '';

            // 移除全局事件监听器
            if (globalEventsAttached) {
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
                globalEventsAttached = false;
            }
        };

        const onMouseDown = (e) => {
            // 清理任何残留状态
            cleanupResizeState();

            // 初始化调整大小状态
            isResizing = true;
            startX = e.clientX;
            startY = e.clientY;
            startWidth = parseInt(previewWindow.style.width) || previewWindow.offsetWidth;
            startHeight = parseInt(previewWindow.style.height) || previewWindow.offsetHeight;

            // 添加调整大小样式类
            previewWindow.classList.add('resizing');
            document.body.style.cursor = 'se-resize';
            document.body.style.userSelect = 'none';

            // 绑定全局事件监听器
            document.addEventListener('mousemove', onMouseMove, { passive: false });
            document.addEventListener('mouseup', onMouseUp, { passive: false });
            globalEventsAttached = true;

            console.log('🎯 开始调整窗口大小:', previewWindow.id, `${startWidth}x${startHeight}`);

            e.preventDefault();
            e.stopPropagation();
        };

        const updateSize = () => {
            if (!isResizing || !pendingUpdate) return;

            const deltaX = currentMouseX - startX;
            const deltaY = currentMouseY - startY;

            const newWidth = Math.max(this.PERFORMANCE_CONSTANTS.MIN_WINDOW_WIDTH, startWidth + deltaX);
            const newHeight = Math.max(this.PERFORMANCE_CONSTANTS.MIN_WINDOW_HEIGHT, startHeight + deltaY);

            // 限制在视口内
            const windowLeft = parseInt(previewWindow.style.left) || 0;
            const windowTop = parseInt(previewWindow.style.top) || 0;
            const maxWidth = window.innerWidth - windowLeft - this.PERFORMANCE_CONSTANTS.WINDOW_MARGIN;
            const maxHeight = window.innerHeight - windowTop - this.PERFORMANCE_CONSTANTS.WINDOW_MARGIN;

            const finalWidth = Math.min(newWidth, maxWidth);
            const finalHeight = Math.min(newHeight, maxHeight);

            previewWindow.style.width = `${finalWidth}px`;
            previewWindow.style.height = `${finalHeight}px`;

            pendingUpdate = false;
            animationFrameId = null;
        };

        const onMouseMove = (e) => {
            if (!isResizing) return;

            // 使用共享的范围限制方法
            const limitedMouse = this.limitMouseRange(e.clientX, e.clientY, resizeHandle, 'se');
            currentMouseX = limitedMouse.x;
            currentMouseY = limitedMouse.y;

            // 使用 requestAnimationFrame 优化渲染性能
            if (!pendingUpdate) {
                pendingUpdate = true;
                animationFrameId = requestAnimationFrame(updateSize);
            }

            e.preventDefault();
        };

        const onMouseUp = (e) => {
            if (!isResizing) return;

            // 标记为用户手动调整大小
            const windowId = previewWindow.id;
            const userAdjustment = this.userAdjustments.get(windowId) || { size: false, position: false };
            userAdjustment.size = true;
            this.userAdjustments.set(windowId, userAdjustment);

            const finalWidth = parseInt(previewWindow.style.width);
            const finalHeight = parseInt(previewWindow.style.height);

            // 延迟保存大小到记忆，避免频繁存储操作
            if (saveTimeout) {
                clearTimeout(saveTimeout);
            }
            saveTimeout = setTimeout(() => {
                this.saveWindowSize(finalWidth, finalHeight);
                saveTimeout = null;
            }, this.PERFORMANCE_CONSTANTS.SAVE_DELAY);

            LinkWindowLogger.success(`窗口大小调整完成: ${previewWindow.id}`, `${finalWidth}x${finalHeight}`);

            cleanupResizeState();
            e.preventDefault();
        };

        resizeHandle.addEventListener('mousedown', onMouseDown, { passive: false });

        // 返回清理函数
        return () => {
            resizeHandle.removeEventListener('mousedown', onMouseDown);
            cleanupResizeState();
        };
    }

    closePreview(windowId) {
        const previewWindow = this.previewWindows.get(windowId);
        if (previewWindow) {
            // 清理事件监听器
            if (previewWindow._eventCleanup) {
                previewWindow._eventCleanup();
            }

            // 清理z-index记录
            this.zIndexManager.removeWindow(windowId);

            LinkWindowUtils.safeRemoveElement(previewWindow);
            this.previewWindows.delete(windowId);
            console.log(`🗑️ 预览窗口已关闭: ${windowId}`);
        }
    }

    loadStyles() {
        if (document.getElementById('moment-linkwindow-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'moment-linkwindow-styles';
        style.textContent = `
            .moment-linkwindow-preview {
                position: fixed;
                background: #fff;
                border: 1px solid #ccc;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                z-index: 999999;
                font-family: Arial, sans-serif;
                font-size: 14px;
                overflow: hidden;
                min-width: 300px;
                min-height: 200px;
            }
            
            .preview-header {
                background: #f5f5f5;
                border-bottom: 1px solid #ddd;
                padding: 8px 12px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
                gap: 10px;
            }
            
            .preview-url-bar {
                flex: 1;
                font-size: 12px;
                color: #666;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            
            .preview-title {
                font-weight: bold;
                color: #333;
            }
            
            .preview-controls {
                display: flex;
                gap: 5px;
            }
            
            .preview-controls button {
                background: none;
                border: none;
                cursor: pointer;
                padding: 2px 5px;
                border-radius: 3px;
                font-size: 12px;
            }
            
            .preview-controls button:hover {
                background: rgba(0,0,0,0.1);
            }
            
            .preview-content {
                flex: 1;
                position: relative;
                height: calc(100% - 40px);
            }
            
            .preview-content iframe {
                width: 100%;
                height: 100%;
                border: none;
            }
            
            .preview-loading {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: #666;
                z-index: 1;
            }
            
            .preview-resize-handle {
                position: absolute;
                bottom: 0;
                right: 0;
                width: 15px;
                height: 15px;
                cursor: se-resize;
                background: rgba(0,0,0,0.1);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                color: #666;
            }
            
            .moment-linkwindow-preview.minimized {
                height: 40px !important;
            }
            
            .text-drag-preview .preview-header {
                background: linear-gradient(135deg, #4facfe, #00f2fe) !important;
                color: white;
            }
            
            .text-drag-preview .preview-title {
                color: white;
            }
        `;
        
        document.head.appendChild(style);
    }
}

// 初始化（优化：简化重复初始化保护逻辑）
function init() {
    // 防止重复初始化
    if (window.momentLinkWindowInitialized) {
        console.log('ℹ️ Moment-LinkWindow 已初始化，跳过');
        return;
    }

    try {
        // 标记已初始化
        window.momentLinkWindowInitialized = true;

        // 初始化管理器
        window.linkWindowTextDragManager = new LinkWindowTextDragManager();
        window.linkWindowPreviewManager = new LinkWindowPreviewManager();

        LinkWindowLogger.success('Moment-LinkWindow 内容脚本已初始化');
    } catch (error) {
        console.error('❌ Moment-LinkWindow 初始化失败:', error);
        // 重置标记以允许重试
        window.momentLinkWindowInitialized = false;
        throw error; // 重新抛出错误以便调试
    }
}

// 启动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}


